﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}"
	ProjectSection(ProjectDependencies) = postProject
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD} = {3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}
		{A793BB86-369B-319D-9A45-6BFB4AD5F169} = {A793BB86-369B-319D-9A45-6BFB4AD5F169}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{EDCD49CE-107D-301E-9C72-10B23008C71A}"
	ProjectSection(ProjectDependencies) = postProject
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615} = {B88402CB-D6D4-3FD4-98BE-580E4FC0E615}
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD} = {3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{AD9A7588-EEEF-3610-8081-D8911ED2C905}"
	ProjectSection(ProjectDependencies) = postProject
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD} = {3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_secure_storage_windows_plugin", "flutter_secure_storage_windows_plugin.vcxproj", "{A793BB86-369B-319D-9A45-6BFB4AD5F169}"
	ProjectSection(ProjectDependencies) = postProject
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD} = {3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}
		{AD9A7588-EEEF-3610-8081-D8911ED2C905} = {AD9A7588-EEEF-3610-8081-D8911ED2C905}
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360} = {828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}"
	ProjectSection(ProjectDependencies) = postProject
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD} = {3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}
		{AD9A7588-EEEF-3610-8081-D8911ED2C905} = {AD9A7588-EEEF-3610-8081-D8911ED2C905}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}.Debug|x64.ActiveCfg = Debug|x64
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}.Debug|x64.Build.0 = Debug|x64
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}.Profile|x64.ActiveCfg = Profile|x64
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}.Profile|x64.Build.0 = Profile|x64
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}.Release|x64.ActiveCfg = Release|x64
		{B88402CB-D6D4-3FD4-98BE-580E4FC0E615}.Release|x64.Build.0 = Release|x64
		{EDCD49CE-107D-301E-9C72-10B23008C71A}.Debug|x64.ActiveCfg = Debug|x64
		{EDCD49CE-107D-301E-9C72-10B23008C71A}.Profile|x64.ActiveCfg = Profile|x64
		{EDCD49CE-107D-301E-9C72-10B23008C71A}.Release|x64.ActiveCfg = Release|x64
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}.Debug|x64.ActiveCfg = Debug|x64
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}.Debug|x64.Build.0 = Debug|x64
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}.Profile|x64.ActiveCfg = Profile|x64
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}.Profile|x64.Build.0 = Profile|x64
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}.Release|x64.ActiveCfg = Release|x64
		{3E0A1FE8-F3A7-3718-9F8E-8BB73D9638DD}.Release|x64.Build.0 = Release|x64
		{AD9A7588-EEEF-3610-8081-D8911ED2C905}.Debug|x64.ActiveCfg = Debug|x64
		{AD9A7588-EEEF-3610-8081-D8911ED2C905}.Debug|x64.Build.0 = Debug|x64
		{AD9A7588-EEEF-3610-8081-D8911ED2C905}.Profile|x64.ActiveCfg = Profile|x64
		{AD9A7588-EEEF-3610-8081-D8911ED2C905}.Profile|x64.Build.0 = Profile|x64
		{AD9A7588-EEEF-3610-8081-D8911ED2C905}.Release|x64.ActiveCfg = Release|x64
		{AD9A7588-EEEF-3610-8081-D8911ED2C905}.Release|x64.Build.0 = Release|x64
		{A793BB86-369B-319D-9A45-6BFB4AD5F169}.Debug|x64.ActiveCfg = Debug|x64
		{A793BB86-369B-319D-9A45-6BFB4AD5F169}.Debug|x64.Build.0 = Debug|x64
		{A793BB86-369B-319D-9A45-6BFB4AD5F169}.Profile|x64.ActiveCfg = Profile|x64
		{A793BB86-369B-319D-9A45-6BFB4AD5F169}.Profile|x64.Build.0 = Profile|x64
		{A793BB86-369B-319D-9A45-6BFB4AD5F169}.Release|x64.ActiveCfg = Release|x64
		{A793BB86-369B-319D-9A45-6BFB4AD5F169}.Release|x64.Build.0 = Release|x64
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}.Debug|x64.ActiveCfg = Debug|x64
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}.Debug|x64.Build.0 = Debug|x64
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}.Profile|x64.ActiveCfg = Profile|x64
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}.Profile|x64.Build.0 = Profile|x64
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}.Release|x64.ActiveCfg = Release|x64
		{828C3DF9-E8E8-3FA7-97E2-269BA4BD5360}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4B2D7E75-0816-3E8B-8FD8-016E3CD9D479}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
