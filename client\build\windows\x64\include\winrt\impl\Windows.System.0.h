// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_System_0_H
#define WINRT_Windows_System_0_H
WINRT_EXPORT namespace winrt::Windows::ApplicationModel
{
    struct AppInfo;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct Deferral;
    template <typename T> struct __declspec(empty_bases) EventHandler;
    struct EventRegistrationToken;
    struct HResult;
    struct IAsyncAction;
    template <typename TResult> struct __declspec(empty_bases) IAsyncOperation;
    template <typename T> struct __declspec(empty_bases) IReference;
    struct Point;
    struct Rect;
    template <typename TSender, typename TResult> struct __declspec(empty_bases) TypedEventHandler;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    template <typename T> struct __declspec(empty_bases) IIterable;
    struct IPropertySet;
    template <typename T> struct __declspec(empty_bases) IVectorView;
    template <typename T> struct __declspec(empty_bases) IVector;
    struct ValueSet;
}
WINRT_EXPORT namespace winrt::Windows::Storage
{
    struct IStorageFile;
    struct IStorageFolder;
    struct IStorageItem;
    struct StorageFile;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Search
{
    struct StorageFileQueryResult;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Streams
{
    struct IInputStream;
    struct IOutputStream;
    struct IRandomAccessStreamReference;
}
WINRT_EXPORT namespace winrt::Windows::System::Diagnostics
{
    struct ProcessDiagnosticInfo;
}
WINRT_EXPORT namespace winrt::Windows::System::RemoteSystems
{
    struct RemoteSystemConnectionRequest;
}
WINRT_EXPORT namespace winrt::Windows::UI::Popups
{
    enum class Placement : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::UI::ViewManagement
{
    enum class ViewSizePreference : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::System
{
    enum class AppDiagnosticInfoWatcherStatus : int32_t
    {
        Created = 0,
        Started = 1,
        EnumerationCompleted = 2,
        Stopping = 3,
        Stopped = 4,
        Aborted = 5,
    };
    enum class AppMemoryUsageLevel : int32_t
    {
        Low = 0,
        Medium = 1,
        High = 2,
        OverLimit = 3,
    };
    enum class AppResourceGroupEnergyQuotaState : int32_t
    {
        Unknown = 0,
        Over = 1,
        Under = 2,
    };
    enum class AppResourceGroupExecutionState : int32_t
    {
        Unknown = 0,
        Running = 1,
        Suspending = 2,
        Suspended = 3,
        NotRunning = 4,
    };
    enum class AppResourceGroupInfoWatcherStatus : int32_t
    {
        Created = 0,
        Started = 1,
        EnumerationCompleted = 2,
        Stopping = 3,
        Stopped = 4,
        Aborted = 5,
    };
    enum class AutoUpdateTimeZoneStatus : int32_t
    {
        Attempted = 0,
        TimedOut = 1,
        Failed = 2,
    };
    enum class DiagnosticAccessStatus : int32_t
    {
        Unspecified = 0,
        Denied = 1,
        Limited = 2,
        Allowed = 3,
    };
    enum class DispatcherQueuePriority : int32_t
    {
        Low = -10,
        Normal = 0,
        High = 10,
    };
    enum class LaunchFileStatus : int32_t
    {
        Success = 0,
        AppUnavailable = 1,
        DeniedByPolicy = 2,
        FileTypeNotSupported = 3,
        Unknown = 4,
    };
    enum class LaunchQuerySupportStatus : int32_t
    {
        Available = 0,
        AppNotInstalled = 1,
        AppUnavailable = 2,
        NotSupported = 3,
        Unknown = 4,
    };
    enum class LaunchQuerySupportType : int32_t
    {
        Uri = 0,
        UriForResults = 1,
    };
    enum class LaunchUriStatus : int32_t
    {
        Success = 0,
        AppUnavailable = 1,
        ProtocolUnavailable = 2,
        Unknown = 3,
    };
    enum class PowerState : int32_t
    {
        ConnectedStandby = 0,
        SleepS3 = 1,
    };
    enum class ProcessorArchitecture : int32_t
    {
        X86 = 0,
        Arm = 5,
        X64 = 9,
        Neutral = 11,
        Arm64 = 12,
        X86OnArm64 = 14,
        Unknown = 65535,
    };
    enum class RemoteLaunchUriStatus : int32_t
    {
        Unknown = 0,
        Success = 1,
        AppUnavailable = 2,
        ProtocolUnavailable = 3,
        RemoteSystemUnavailable = 4,
        ValueSetTooLarge = 5,
        DeniedByLocalSystem = 6,
        DeniedByRemoteSystem = 7,
    };
    enum class ShutdownKind : int32_t
    {
        Shutdown = 0,
        Restart = 1,
    };
    enum class UserAuthenticationStatus : int32_t
    {
        Unauthenticated = 0,
        LocallyAuthenticated = 1,
        RemotelyAuthenticated = 2,
    };
    enum class UserPictureSize : int32_t
    {
        Size64x64 = 0,
        Size208x208 = 1,
        Size424x424 = 2,
        Size1080x1080 = 3,
    };
    enum class UserType : int32_t
    {
        LocalUser = 0,
        RemoteUser = 1,
        LocalGuest = 2,
        RemoteGuest = 3,
    };
    enum class UserWatcherStatus : int32_t
    {
        Created = 0,
        Started = 1,
        EnumerationCompleted = 2,
        Stopping = 3,
        Stopped = 4,
        Aborted = 5,
    };
    enum class UserWatcherUpdateKind : int32_t
    {
        Properties = 0,
        Picture = 1,
    };
    enum class VirtualKey : int32_t
    {
        None = 0,
        LeftButton = 1,
        RightButton = 2,
        Cancel = 3,
        MiddleButton = 4,
        XButton1 = 5,
        XButton2 = 6,
        Back = 8,
        Tab = 9,
        Clear = 12,
        Enter = 13,
        Shift = 16,
        Control = 17,
        Menu = 18,
        Pause = 19,
        CapitalLock = 20,
        Kana = 21,
        Hangul = 21,
        ImeOn = 22,
        Junja = 23,
        Final = 24,
        Hanja = 25,
        Kanji = 25,
        ImeOff = 26,
        Escape = 27,
        Convert = 28,
        NonConvert = 29,
        Accept = 30,
        ModeChange = 31,
        Space = 32,
        PageUp = 33,
        PageDown = 34,
        End = 35,
        Home = 36,
        Left = 37,
        Up = 38,
        Right = 39,
        Down = 40,
        Select = 41,
        Print = 42,
        Execute = 43,
        Snapshot = 44,
        Insert = 45,
        Delete = 46,
        Help = 47,
        Number0 = 48,
        Number1 = 49,
        Number2 = 50,
        Number3 = 51,
        Number4 = 52,
        Number5 = 53,
        Number6 = 54,
        Number7 = 55,
        Number8 = 56,
        Number9 = 57,
        A = 65,
        B = 66,
        C = 67,
        D = 68,
        E = 69,
        F = 70,
        G = 71,
        H = 72,
        I = 73,
        J = 74,
        K = 75,
        L = 76,
        M = 77,
        N = 78,
        O = 79,
        P = 80,
        Q = 81,
        R = 82,
        S = 83,
        T = 84,
        U = 85,
        V = 86,
        W = 87,
        X = 88,
        Y = 89,
        Z = 90,
        LeftWindows = 91,
        RightWindows = 92,
        Application = 93,
        Sleep = 95,
        NumberPad0 = 96,
        NumberPad1 = 97,
        NumberPad2 = 98,
        NumberPad3 = 99,
        NumberPad4 = 100,
        NumberPad5 = 101,
        NumberPad6 = 102,
        NumberPad7 = 103,
        NumberPad8 = 104,
        NumberPad9 = 105,
        Multiply = 106,
        Add = 107,
        Separator = 108,
        Subtract = 109,
        Decimal = 110,
        Divide = 111,
        F1 = 112,
        F2 = 113,
        F3 = 114,
        F4 = 115,
        F5 = 116,
        F6 = 117,
        F7 = 118,
        F8 = 119,
        F9 = 120,
        F10 = 121,
        F11 = 122,
        F12 = 123,
        F13 = 124,
        F14 = 125,
        F15 = 126,
        F16 = 127,
        F17 = 128,
        F18 = 129,
        F19 = 130,
        F20 = 131,
        F21 = 132,
        F22 = 133,
        F23 = 134,
        F24 = 135,
        NavigationView = 136,
        NavigationMenu = 137,
        NavigationUp = 138,
        NavigationDown = 139,
        NavigationLeft = 140,
        NavigationRight = 141,
        NavigationAccept = 142,
        NavigationCancel = 143,
        NumberKeyLock = 144,
        Scroll = 145,
        LeftShift = 160,
        RightShift = 161,
        LeftControl = 162,
        RightControl = 163,
        LeftMenu = 164,
        RightMenu = 165,
        GoBack = 166,
        GoForward = 167,
        Refresh = 168,
        Stop = 169,
        Search = 170,
        Favorites = 171,
        GoHome = 172,
        GamepadA = 195,
        GamepadB = 196,
        GamepadX = 197,
        GamepadY = 198,
        GamepadRightShoulder = 199,
        GamepadLeftShoulder = 200,
        GamepadLeftTrigger = 201,
        GamepadRightTrigger = 202,
        GamepadDPadUp = 203,
        GamepadDPadDown = 204,
        GamepadDPadLeft = 205,
        GamepadDPadRight = 206,
        GamepadMenu = 207,
        GamepadView = 208,
        GamepadLeftThumbstickButton = 209,
        GamepadRightThumbstickButton = 210,
        GamepadLeftThumbstickUp = 211,
        GamepadLeftThumbstickDown = 212,
        GamepadLeftThumbstickRight = 213,
        GamepadLeftThumbstickLeft = 214,
        GamepadRightThumbstickUp = 215,
        GamepadRightThumbstickDown = 216,
        GamepadRightThumbstickRight = 217,
        GamepadRightThumbstickLeft = 218,
    };
    enum class VirtualKeyModifiers : uint32_t
    {
        None = 0,
        Control = 0x1,
        Menu = 0x2,
        Shift = 0x4,
        Windows = 0x8,
    };
    struct IAppActivationResult;
    struct IAppDiagnosticInfo;
    struct IAppDiagnosticInfo2;
    struct IAppDiagnosticInfo3;
    struct IAppDiagnosticInfoStatics;
    struct IAppDiagnosticInfoStatics2;
    struct IAppDiagnosticInfoWatcher;
    struct IAppDiagnosticInfoWatcherEventArgs;
    struct IAppExecutionStateChangeResult;
    struct IAppMemoryReport;
    struct IAppMemoryReport2;
    struct IAppMemoryUsageLimitChangingEventArgs;
    struct IAppResourceGroupBackgroundTaskReport;
    struct IAppResourceGroupInfo;
    struct IAppResourceGroupInfo2;
    struct IAppResourceGroupInfoWatcher;
    struct IAppResourceGroupInfoWatcherEventArgs;
    struct IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs;
    struct IAppResourceGroupMemoryReport;
    struct IAppResourceGroupStateReport;
    struct IAppUriHandlerHost;
    struct IAppUriHandlerHostFactory;
    struct IAppUriHandlerRegistration;
    struct IAppUriHandlerRegistrationManager;
    struct IAppUriHandlerRegistrationManagerStatics;
    struct IDateTimeSettingsStatics;
    struct IDispatcherQueue;
    struct IDispatcherQueue2;
    struct IDispatcherQueueController;
    struct IDispatcherQueueControllerStatics;
    struct IDispatcherQueueShutdownStartingEventArgs;
    struct IDispatcherQueueStatics;
    struct IDispatcherQueueTimer;
    struct IFolderLauncherOptions;
    struct IKnownUserPropertiesStatics;
    struct ILaunchUriResult;
    struct ILauncherOptions;
    struct ILauncherOptions2;
    struct ILauncherOptions3;
    struct ILauncherOptions4;
    struct ILauncherStatics;
    struct ILauncherStatics2;
    struct ILauncherStatics3;
    struct ILauncherStatics4;
    struct ILauncherStatics5;
    struct ILauncherUIOptions;
    struct ILauncherViewOptions;
    struct IMemoryManagerStatics;
    struct IMemoryManagerStatics2;
    struct IMemoryManagerStatics3;
    struct IMemoryManagerStatics4;
    struct IProcessLauncherOptions;
    struct IProcessLauncherResult;
    struct IProcessLauncherStatics;
    struct IProcessMemoryReport;
    struct IProtocolForResultsOperation;
    struct IRemoteLauncherOptions;
    struct IRemoteLauncherStatics;
    struct IShutdownManagerStatics;
    struct IShutdownManagerStatics2;
    struct ITimeZoneSettingsStatics;
    struct ITimeZoneSettingsStatics2;
    struct IUser;
    struct IUserAuthenticationStatusChangeDeferral;
    struct IUserAuthenticationStatusChangingEventArgs;
    struct IUserChangedEventArgs;
    struct IUserChangedEventArgs2;
    struct IUserDeviceAssociationChangedEventArgs;
    struct IUserDeviceAssociationStatics;
    struct IUserPicker;
    struct IUserPickerStatics;
    struct IUserStatics;
    struct IUserWatcher;
    struct AppActivationResult;
    struct AppDiagnosticInfo;
    struct AppDiagnosticInfoWatcher;
    struct AppDiagnosticInfoWatcherEventArgs;
    struct AppExecutionStateChangeResult;
    struct AppMemoryReport;
    struct AppMemoryUsageLimitChangingEventArgs;
    struct AppResourceGroupBackgroundTaskReport;
    struct AppResourceGroupInfo;
    struct AppResourceGroupInfoWatcher;
    struct AppResourceGroupInfoWatcherEventArgs;
    struct AppResourceGroupInfoWatcherExecutionStateChangedEventArgs;
    struct AppResourceGroupMemoryReport;
    struct AppResourceGroupStateReport;
    struct AppUriHandlerHost;
    struct AppUriHandlerRegistration;
    struct AppUriHandlerRegistrationManager;
    struct DateTimeSettings;
    struct DispatcherQueue;
    struct DispatcherQueueController;
    struct DispatcherQueueShutdownStartingEventArgs;
    struct DispatcherQueueTimer;
    struct FolderLauncherOptions;
    struct KnownUserProperties;
    struct LaunchUriResult;
    struct Launcher;
    struct LauncherOptions;
    struct LauncherUIOptions;
    struct MemoryManager;
    struct ProcessLauncher;
    struct ProcessLauncherOptions;
    struct ProcessLauncherResult;
    struct ProcessMemoryReport;
    struct ProtocolForResultsOperation;
    struct RemoteLauncher;
    struct RemoteLauncherOptions;
    struct ShutdownManager;
    struct TimeZoneSettings;
    struct User;
    struct UserAuthenticationStatusChangeDeferral;
    struct UserAuthenticationStatusChangingEventArgs;
    struct UserChangedEventArgs;
    struct UserDeviceAssociation;
    struct UserDeviceAssociationChangedEventArgs;
    struct UserPicker;
    struct UserWatcher;
    struct DispatcherQueueHandler;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::System::IAppActivationResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfo>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfo2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfo3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfoStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfoStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfoWatcher>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppDiagnosticInfoWatcherEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppExecutionStateChangeResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppMemoryReport>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppMemoryReport2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppMemoryUsageLimitChangingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupBackgroundTaskReport>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupInfo>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupInfo2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupInfoWatcher>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupInfoWatcherEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupMemoryReport>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppResourceGroupStateReport>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppUriHandlerHost>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppUriHandlerHostFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppUriHandlerRegistration>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppUriHandlerRegistrationManager>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IAppUriHandlerRegistrationManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDateTimeSettingsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueue>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueue2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueueController>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueueControllerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueueShutdownStartingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueueStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IDispatcherQueueTimer>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IFolderLauncherOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IKnownUserPropertiesStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILaunchUriResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherOptions3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherOptions4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherStatics3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherStatics4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherStatics5>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherUIOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ILauncherViewOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IMemoryManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IMemoryManagerStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IMemoryManagerStatics3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IMemoryManagerStatics4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IProcessLauncherOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IProcessLauncherResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IProcessLauncherStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IProcessMemoryReport>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IProtocolForResultsOperation>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IRemoteLauncherOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IRemoteLauncherStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IShutdownManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IShutdownManagerStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ITimeZoneSettingsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::ITimeZoneSettingsStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUser>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserAuthenticationStatusChangeDeferral>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserAuthenticationStatusChangingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserChangedEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserDeviceAssociationChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserDeviceAssociationStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserPicker>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserPickerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::IUserWatcher>{ using type = interface_category; };
    template <> struct category<winrt::Windows::System::AppActivationResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppDiagnosticInfo>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppDiagnosticInfoWatcher>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppExecutionStateChangeResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppMemoryReport>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppMemoryUsageLimitChangingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupBackgroundTaskReport>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupInfo>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupInfoWatcher>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupInfoWatcherExecutionStateChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupMemoryReport>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupStateReport>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppUriHandlerHost>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppUriHandlerRegistration>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppUriHandlerRegistrationManager>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::DateTimeSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::DispatcherQueue>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::DispatcherQueueController>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::DispatcherQueueShutdownStartingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::DispatcherQueueTimer>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::FolderLauncherOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::KnownUserProperties>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::LaunchUriResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::Launcher>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::LauncherOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::LauncherUIOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::MemoryManager>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::ProcessLauncher>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::ProcessLauncherOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::ProcessLauncherResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::ProcessMemoryReport>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::ProtocolForResultsOperation>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::RemoteLauncher>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::RemoteLauncherOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::ShutdownManager>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::TimeZoneSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::User>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserAuthenticationStatusChangeDeferral>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserAuthenticationStatusChangingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserDeviceAssociation>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserDeviceAssociationChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserPicker>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::UserWatcher>{ using type = class_category; };
    template <> struct category<winrt::Windows::System::AppDiagnosticInfoWatcherStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::AppMemoryUsageLevel>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupEnergyQuotaState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupExecutionState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::AppResourceGroupInfoWatcherStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::AutoUpdateTimeZoneStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::DiagnosticAccessStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::DispatcherQueuePriority>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::LaunchFileStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::LaunchQuerySupportStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::LaunchQuerySupportType>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::LaunchUriStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::PowerState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::ProcessorArchitecture>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::RemoteLaunchUriStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::ShutdownKind>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::UserAuthenticationStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::UserPictureSize>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::UserType>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::UserWatcherStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::UserWatcherUpdateKind>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::VirtualKey>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::VirtualKeyModifiers>{ using type = enum_category; };
    template <> struct category<winrt::Windows::System::DispatcherQueueHandler>{ using type = delegate_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppActivationResult> = L"Windows.System.AppActivationResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppDiagnosticInfo> = L"Windows.System.AppDiagnosticInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppDiagnosticInfoWatcher> = L"Windows.System.AppDiagnosticInfoWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs> = L"Windows.System.AppDiagnosticInfoWatcherEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppExecutionStateChangeResult> = L"Windows.System.AppExecutionStateChangeResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppMemoryReport> = L"Windows.System.AppMemoryReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppMemoryUsageLimitChangingEventArgs> = L"Windows.System.AppMemoryUsageLimitChangingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupBackgroundTaskReport> = L"Windows.System.AppResourceGroupBackgroundTaskReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupInfo> = L"Windows.System.AppResourceGroupInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupInfoWatcher> = L"Windows.System.AppResourceGroupInfoWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs> = L"Windows.System.AppResourceGroupInfoWatcherEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupInfoWatcherExecutionStateChangedEventArgs> = L"Windows.System.AppResourceGroupInfoWatcherExecutionStateChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupMemoryReport> = L"Windows.System.AppResourceGroupMemoryReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupStateReport> = L"Windows.System.AppResourceGroupStateReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppUriHandlerHost> = L"Windows.System.AppUriHandlerHost";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppUriHandlerRegistration> = L"Windows.System.AppUriHandlerRegistration";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppUriHandlerRegistrationManager> = L"Windows.System.AppUriHandlerRegistrationManager";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DateTimeSettings> = L"Windows.System.DateTimeSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DispatcherQueue> = L"Windows.System.DispatcherQueue";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DispatcherQueueController> = L"Windows.System.DispatcherQueueController";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DispatcherQueueShutdownStartingEventArgs> = L"Windows.System.DispatcherQueueShutdownStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DispatcherQueueTimer> = L"Windows.System.DispatcherQueueTimer";
    template <> inline constexpr auto& name_v<winrt::Windows::System::FolderLauncherOptions> = L"Windows.System.FolderLauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::KnownUserProperties> = L"Windows.System.KnownUserProperties";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LaunchUriResult> = L"Windows.System.LaunchUriResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::Launcher> = L"Windows.System.Launcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LauncherOptions> = L"Windows.System.LauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LauncherUIOptions> = L"Windows.System.LauncherUIOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::MemoryManager> = L"Windows.System.MemoryManager";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ProcessLauncher> = L"Windows.System.ProcessLauncher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ProcessLauncherOptions> = L"Windows.System.ProcessLauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ProcessLauncherResult> = L"Windows.System.ProcessLauncherResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ProcessMemoryReport> = L"Windows.System.ProcessMemoryReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ProtocolForResultsOperation> = L"Windows.System.ProtocolForResultsOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::System::RemoteLauncher> = L"Windows.System.RemoteLauncher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::RemoteLauncherOptions> = L"Windows.System.RemoteLauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ShutdownManager> = L"Windows.System.ShutdownManager";
    template <> inline constexpr auto& name_v<winrt::Windows::System::TimeZoneSettings> = L"Windows.System.TimeZoneSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::System::User> = L"Windows.System.User";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserAuthenticationStatusChangeDeferral> = L"Windows.System.UserAuthenticationStatusChangeDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserAuthenticationStatusChangingEventArgs> = L"Windows.System.UserAuthenticationStatusChangingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserChangedEventArgs> = L"Windows.System.UserChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserDeviceAssociation> = L"Windows.System.UserDeviceAssociation";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserDeviceAssociationChangedEventArgs> = L"Windows.System.UserDeviceAssociationChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserPicker> = L"Windows.System.UserPicker";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserWatcher> = L"Windows.System.UserWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppDiagnosticInfoWatcherStatus> = L"Windows.System.AppDiagnosticInfoWatcherStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppMemoryUsageLevel> = L"Windows.System.AppMemoryUsageLevel";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupEnergyQuotaState> = L"Windows.System.AppResourceGroupEnergyQuotaState";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupExecutionState> = L"Windows.System.AppResourceGroupExecutionState";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AppResourceGroupInfoWatcherStatus> = L"Windows.System.AppResourceGroupInfoWatcherStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::AutoUpdateTimeZoneStatus> = L"Windows.System.AutoUpdateTimeZoneStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DiagnosticAccessStatus> = L"Windows.System.DiagnosticAccessStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DispatcherQueuePriority> = L"Windows.System.DispatcherQueuePriority";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LaunchFileStatus> = L"Windows.System.LaunchFileStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LaunchQuerySupportStatus> = L"Windows.System.LaunchQuerySupportStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LaunchQuerySupportType> = L"Windows.System.LaunchQuerySupportType";
    template <> inline constexpr auto& name_v<winrt::Windows::System::LaunchUriStatus> = L"Windows.System.LaunchUriStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::PowerState> = L"Windows.System.PowerState";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ProcessorArchitecture> = L"Windows.System.ProcessorArchitecture";
    template <> inline constexpr auto& name_v<winrt::Windows::System::RemoteLaunchUriStatus> = L"Windows.System.RemoteLaunchUriStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ShutdownKind> = L"Windows.System.ShutdownKind";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserAuthenticationStatus> = L"Windows.System.UserAuthenticationStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserPictureSize> = L"Windows.System.UserPictureSize";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserType> = L"Windows.System.UserType";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserWatcherStatus> = L"Windows.System.UserWatcherStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::System::UserWatcherUpdateKind> = L"Windows.System.UserWatcherUpdateKind";
    template <> inline constexpr auto& name_v<winrt::Windows::System::VirtualKey> = L"Windows.System.VirtualKey";
    template <> inline constexpr auto& name_v<winrt::Windows::System::VirtualKeyModifiers> = L"Windows.System.VirtualKeyModifiers";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppActivationResult> = L"Windows.System.IAppActivationResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfo> = L"Windows.System.IAppDiagnosticInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfo2> = L"Windows.System.IAppDiagnosticInfo2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfo3> = L"Windows.System.IAppDiagnosticInfo3";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfoStatics> = L"Windows.System.IAppDiagnosticInfoStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfoStatics2> = L"Windows.System.IAppDiagnosticInfoStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfoWatcher> = L"Windows.System.IAppDiagnosticInfoWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppDiagnosticInfoWatcherEventArgs> = L"Windows.System.IAppDiagnosticInfoWatcherEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppExecutionStateChangeResult> = L"Windows.System.IAppExecutionStateChangeResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppMemoryReport> = L"Windows.System.IAppMemoryReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppMemoryReport2> = L"Windows.System.IAppMemoryReport2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppMemoryUsageLimitChangingEventArgs> = L"Windows.System.IAppMemoryUsageLimitChangingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupBackgroundTaskReport> = L"Windows.System.IAppResourceGroupBackgroundTaskReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupInfo> = L"Windows.System.IAppResourceGroupInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupInfo2> = L"Windows.System.IAppResourceGroupInfo2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupInfoWatcher> = L"Windows.System.IAppResourceGroupInfoWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupInfoWatcherEventArgs> = L"Windows.System.IAppResourceGroupInfoWatcherEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs> = L"Windows.System.IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupMemoryReport> = L"Windows.System.IAppResourceGroupMemoryReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppResourceGroupStateReport> = L"Windows.System.IAppResourceGroupStateReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppUriHandlerHost> = L"Windows.System.IAppUriHandlerHost";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppUriHandlerHostFactory> = L"Windows.System.IAppUriHandlerHostFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppUriHandlerRegistration> = L"Windows.System.IAppUriHandlerRegistration";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppUriHandlerRegistrationManager> = L"Windows.System.IAppUriHandlerRegistrationManager";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IAppUriHandlerRegistrationManagerStatics> = L"Windows.System.IAppUriHandlerRegistrationManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDateTimeSettingsStatics> = L"Windows.System.IDateTimeSettingsStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueue> = L"Windows.System.IDispatcherQueue";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueue2> = L"Windows.System.IDispatcherQueue2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueueController> = L"Windows.System.IDispatcherQueueController";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueueControllerStatics> = L"Windows.System.IDispatcherQueueControllerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueueShutdownStartingEventArgs> = L"Windows.System.IDispatcherQueueShutdownStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueueStatics> = L"Windows.System.IDispatcherQueueStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IDispatcherQueueTimer> = L"Windows.System.IDispatcherQueueTimer";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IFolderLauncherOptions> = L"Windows.System.IFolderLauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IKnownUserPropertiesStatics> = L"Windows.System.IKnownUserPropertiesStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILaunchUriResult> = L"Windows.System.ILaunchUriResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherOptions> = L"Windows.System.ILauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherOptions2> = L"Windows.System.ILauncherOptions2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherOptions3> = L"Windows.System.ILauncherOptions3";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherOptions4> = L"Windows.System.ILauncherOptions4";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherStatics> = L"Windows.System.ILauncherStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherStatics2> = L"Windows.System.ILauncherStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherStatics3> = L"Windows.System.ILauncherStatics3";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherStatics4> = L"Windows.System.ILauncherStatics4";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherStatics5> = L"Windows.System.ILauncherStatics5";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherUIOptions> = L"Windows.System.ILauncherUIOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ILauncherViewOptions> = L"Windows.System.ILauncherViewOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IMemoryManagerStatics> = L"Windows.System.IMemoryManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IMemoryManagerStatics2> = L"Windows.System.IMemoryManagerStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IMemoryManagerStatics3> = L"Windows.System.IMemoryManagerStatics3";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IMemoryManagerStatics4> = L"Windows.System.IMemoryManagerStatics4";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IProcessLauncherOptions> = L"Windows.System.IProcessLauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IProcessLauncherResult> = L"Windows.System.IProcessLauncherResult";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IProcessLauncherStatics> = L"Windows.System.IProcessLauncherStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IProcessMemoryReport> = L"Windows.System.IProcessMemoryReport";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IProtocolForResultsOperation> = L"Windows.System.IProtocolForResultsOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IRemoteLauncherOptions> = L"Windows.System.IRemoteLauncherOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IRemoteLauncherStatics> = L"Windows.System.IRemoteLauncherStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IShutdownManagerStatics> = L"Windows.System.IShutdownManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IShutdownManagerStatics2> = L"Windows.System.IShutdownManagerStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ITimeZoneSettingsStatics> = L"Windows.System.ITimeZoneSettingsStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::ITimeZoneSettingsStatics2> = L"Windows.System.ITimeZoneSettingsStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUser> = L"Windows.System.IUser";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserAuthenticationStatusChangeDeferral> = L"Windows.System.IUserAuthenticationStatusChangeDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserAuthenticationStatusChangingEventArgs> = L"Windows.System.IUserAuthenticationStatusChangingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserChangedEventArgs> = L"Windows.System.IUserChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserChangedEventArgs2> = L"Windows.System.IUserChangedEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserDeviceAssociationChangedEventArgs> = L"Windows.System.IUserDeviceAssociationChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserDeviceAssociationStatics> = L"Windows.System.IUserDeviceAssociationStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserPicker> = L"Windows.System.IUserPicker";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserPickerStatics> = L"Windows.System.IUserPickerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserStatics> = L"Windows.System.IUserStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::System::IUserWatcher> = L"Windows.System.IUserWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::System::DispatcherQueueHandler> = L"Windows.System.DispatcherQueueHandler";
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppActivationResult>{ 0x6B528900,0xF46E,0x4EB0,{ 0xAA,0x6C,0x38,0xAF,0x55,0x7C,0xF9,0xED } }; // 6B528900-F46E-4EB0-AA6C-38AF557CF9ED
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfo>{ 0xE348A69A,0x8889,0x4CA3,{ 0xBE,0x07,0xD5,0xFF,0xFF,0x5F,0x08,0x04 } }; // E348A69A-8889-4CA3-BE07-D5FFFF5F0804
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfo2>{ 0xDF46FBD7,0x191A,0x446C,{ 0x94,0x73,0x8F,0xBC,0x23,0x74,0xA3,0x54 } }; // DF46FBD7-191A-446C-9473-8FBC2374A354
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfo3>{ 0xC895C63D,0xDD61,0x4C65,{ 0xBA,0xBD,0x81,0xA1,0x0B,0x4F,0x98,0x15 } }; // C895C63D-DD61-4C65-BABD-81A10B4F9815
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfoStatics>{ 0xCE6925BF,0x10CA,0x40C8,{ 0xA9,0xCA,0xC5,0xC9,0x65,0x01,0x86,0x6E } }; // CE6925BF-10CA-40C8-A9CA-C5C96501866E
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfoStatics2>{ 0x05B24B86,0x1000,0x4C90,{ 0xBB,0x9F,0x72,0x35,0x07,0x1C,0x50,0xFE } }; // 05B24B86-1000-4C90-BB9F-7235071C50FE
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfoWatcher>{ 0x75575070,0x01D3,0x489A,{ 0x93,0x25,0x52,0xF9,0xCC,0x6E,0xDE,0x0A } }; // 75575070-01D3-489A-9325-52F9CC6EDE0A
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppDiagnosticInfoWatcherEventArgs>{ 0x7017C716,0xE1DA,0x4C65,{ 0x99,0xDF,0x04,0x6D,0xFF,0x5B,0xE7,0x1A } }; // 7017C716-E1DA-4C65-99DF-046DFF5BE71A
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppExecutionStateChangeResult>{ 0x6F039BF0,0xF91B,0x4DF8,{ 0xAE,0x77,0x30,0x33,0xCC,0xB6,0x91,0x14 } }; // 6F039BF0-F91B-4DF8-AE77-3033CCB69114
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppMemoryReport>{ 0x6D65339B,0x4D6F,0x45BC,{ 0x9C,0x5E,0xE4,0x9B,0x3F,0xF2,0x75,0x8D } }; // 6D65339B-4D6F-45BC-9C5E-E49B3FF2758D
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppMemoryReport2>{ 0x5F7F3738,0x51B7,0x42DC,{ 0xB7,0xED,0x79,0xBA,0x46,0xD2,0x88,0x57 } }; // 5F7F3738-51B7-42DC-B7ED-79BA46D28857
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppMemoryUsageLimitChangingEventArgs>{ 0x79F86664,0xFECA,0x4DA5,{ 0x9E,0x40,0x2B,0xC6,0x3E,0xFD,0xC9,0x79 } }; // 79F86664-FECA-4DA5-9E40-2BC63EFDC979
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupBackgroundTaskReport>{ 0x2566E74E,0xB05D,0x40C2,{ 0x9D,0xC1,0x1A,0x4F,0x03,0x9E,0xA1,0x20 } }; // 2566E74E-B05D-40C2-9DC1-1A4F039EA120
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupInfo>{ 0xB913F77A,0xE807,0x49F4,{ 0x84,0x5E,0x7B,0x8B,0xDC,0xFE,0x8E,0xE7 } }; // B913F77A-E807-49F4-845E-7B8BDCFE8EE7
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupInfo2>{ 0xEE9B236D,0xD305,0x4D6B,{ 0x92,0xF7,0x6A,0xFD,0xAD,0x72,0xDE,0xDC } }; // EE9B236D-D305-4D6B-92F7-6AFDAD72DEDC
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupInfoWatcher>{ 0xD9B0A0FD,0x6E5A,0x4C72,{ 0x8B,0x17,0x09,0xFE,0xC4,0xA2,0x12,0xBD } }; // D9B0A0FD-6E5A-4C72-8B17-09FEC4A212BD
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupInfoWatcherEventArgs>{ 0x7A787637,0x6302,0x4D2F,{ 0xBF,0x89,0x1C,0x12,0xD0,0xB2,0xA6,0xB9 } }; // 7A787637-6302-4D2F-BF89-1C12D0B2A6B9
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs>{ 0x1BDBEDD7,0xFEE6,0x4FD4,{ 0x98,0xDD,0xE9,0x2A,0x2C,0xC2,0x99,0xF3 } }; // 1BDBEDD7-FEE6-4FD4-98DD-E92A2CC299F3
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupMemoryReport>{ 0x2C8C06B1,0x7DB1,0x4C51,{ 0xA2,0x25,0x7F,0xAE,0x2D,0x49,0xE4,0x31 } }; // 2C8C06B1-7DB1-4C51-A225-7FAE2D49E431
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppResourceGroupStateReport>{ 0x52849F18,0x2F70,0x4236,{ 0xAB,0x40,0xD0,0x4D,0xB0,0xC7,0xB9,0x31 } }; // 52849F18-2F70-4236-AB40-D04DB0C7B931
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppUriHandlerHost>{ 0x5D50CAC5,0x92D2,0x5409,{ 0xB5,0x6F,0x7F,0x73,0xE1,0x0E,0xA4,0xC3 } }; // 5D50CAC5-92D2-5409-B56F-7F73E10EA4C3
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppUriHandlerHostFactory>{ 0x257C3C96,0xCE04,0x5F98,{ 0x96,0xBB,0x3E,0xBD,0x3E,0x92,0x75,0xBB } }; // 257C3C96-CE04-5F98-96BB-3EBD3E9275BB
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppUriHandlerRegistration>{ 0x6F73AEB1,0x4569,0x5C3F,{ 0x9B,0xA0,0x99,0x12,0x3E,0xEA,0x32,0xC3 } }; // 6F73AEB1-4569-5C3F-9BA0-99123EEA32C3
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppUriHandlerRegistrationManager>{ 0xE62C9A52,0xAC94,0x5750,{ 0xAC,0x1B,0x6C,0xFB,0x6F,0x25,0x02,0x63 } }; // E62C9A52-AC94-5750-AC1B-6CFB6F250263
    template <> inline constexpr guid guid_v<winrt::Windows::System::IAppUriHandlerRegistrationManagerStatics>{ 0xD5CEDD9F,0x5729,0x5B76,{ 0xA1,0xD4,0x02,0x85,0xF2,0x95,0xC1,0x24 } }; // D5CEDD9F-5729-5B76-A1D4-0285F295C124
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDateTimeSettingsStatics>{ 0x5D2150D1,0x47EE,0x48AB,{ 0xA5,0x2B,0x9F,0x19,0x54,0x27,0x8D,0x82 } }; // 5D2150D1-47EE-48AB-A52B-9F1954278D82
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueue>{ 0x603E88E4,0xA338,0x4FFE,{ 0xA4,0x57,0xA5,0xCF,0xB9,0xCE,0xB8,0x99 } }; // 603E88E4-A338-4FFE-A457-A5CFB9CEB899
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueue2>{ 0xC822C647,0x30EF,0x506E,{ 0xBD,0x1E,0xA6,0x47,0xAE,0x66,0x75,0xFF } }; // C822C647-30EF-506E-BD1E-A647AE6675FF
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueueController>{ 0x22F34E66,0x50DB,0x4E36,{ 0xA9,0x8D,0x61,0xC0,0x1B,0x38,0x4D,0x20 } }; // 22F34E66-50DB-4E36-A98D-61C01B384D20
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueueControllerStatics>{ 0x0A6C98E0,0x5198,0x49A2,{ 0xA3,0x13,0x3F,0x70,0xD1,0xF1,0x3C,0x27 } }; // 0A6C98E0-5198-49A2-A313-3F70D1F13C27
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueueShutdownStartingEventArgs>{ 0xC4724C4C,0xFF97,0x40C0,{ 0xA2,0x26,0xCC,0x0A,0xAA,0x54,0x5E,0x89 } }; // C4724C4C-FF97-40C0-A226-CC0AAA545E89
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueueStatics>{ 0xA96D83D7,0x9371,0x4517,{ 0x92,0x45,0xD0,0x82,0x4A,0xC1,0x2C,0x74 } }; // A96D83D7-**************-D0824AC12C74
    template <> inline constexpr guid guid_v<winrt::Windows::System::IDispatcherQueueTimer>{ 0x5FEABB1D,0xA31C,0x4727,{ 0xB1,0xAC,0x37,0x45,0x46,0x49,0xD5,0x6A } }; // 5FEABB1D-A31C-4727-B1AC-37454649D56A
    template <> inline constexpr guid guid_v<winrt::Windows::System::IFolderLauncherOptions>{ 0xBB91C27D,0x6B87,0x432A,{ 0xBD,0x04,0x77,0x6C,0x6F,0x5F,0xB2,0xAB } }; // BB91C27D-6B87-432A-BD04-776C6F5FB2AB
    template <> inline constexpr guid guid_v<winrt::Windows::System::IKnownUserPropertiesStatics>{ 0x7755911A,0x70C5,0x48E5,{ 0xB6,0x37,0x5B,0xA3,0x44,0x1E,0x4E,0xE4 } }; // 7755911A-70C5-48E5-B637-5BA3441E4EE4
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILaunchUriResult>{ 0xEC27A8DF,0xF6D5,0x45CA,{ 0x91,0x3A,0x70,0xA4,0x0C,0x5C,0x82,0x21 } }; // EC27A8DF-F6D5-45CA-913A-70A40C5C8221
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherOptions>{ 0xBAFA21D8,0xB071,0x4CD8,{ 0x85,0x3E,0x34,0x12,0x03,0xE5,0x57,0xD3 } }; // BAFA21D8-B071-4CD8-853E-341203E557D3
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherOptions2>{ 0x3BA08EB4,0x6E40,0x4DCE,{ 0xA1,0xA3,0x2F,0x53,0x95,0x0A,0xFB,0x49 } }; // 3BA08EB4-6E40-4DCE-A1A3-2F53950AFB49
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherOptions3>{ 0xF0770655,0x4B63,0x4E3A,{ 0x91,0x07,0x4E,0x68,0x78,0x41,0x92,0x3A } }; // F0770655-4B63-4E3A-9107-4E687841923A
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherOptions4>{ 0xEF6FD10E,0xE6FB,0x4814,{ 0xA4,0x4E,0x57,0xE8,0xB9,0xD9,0xA0,0x1B } }; // EF6FD10E-E6FB-4814-A44E-57E8B9D9A01B
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherStatics>{ 0x277151C3,0x9E3E,0x42F6,{ 0x91,0xA4,0x5D,0xFD,0xEB,0x23,0x24,0x51 } }; // 277151C3-9E3E-42F6-91A4-5DFDEB232451
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherStatics2>{ 0x59BA2FBB,0x24CB,0x4C02,{ 0xA4,0xC4,0x82,0x94,0x56,0x9D,0x54,0xF1 } }; // 59BA2FBB-24CB-4C02-A4C4-8294569D54F1
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherStatics3>{ 0x234261A8,0x9DB3,0x4683,{ 0xAA,0x42,0xDC,0x6F,0x51,0xD3,0x38,0x47 } }; // 234261A8-9DB3-4683-AA42-DC6F51D33847
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherStatics4>{ 0xB9EC819F,0xB5A5,0x41C6,{ 0xB3,0xB3,0xDD,0x1B,0x31,0x78,0xBC,0xF2 } }; // B9EC819F-B5A5-41C6-B3B3-DD1B3178BCF2
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherStatics5>{ 0x5B24EF84,0xD895,0x5FEA,{ 0x91,0x53,0x1A,0xC4,0x9A,0xED,0x9B,0xA9 } }; // 5B24EF84-D895-5FEA-9153-1AC49AED9BA9
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherUIOptions>{ 0x1B25DA6E,0x8AA6,0x41E9,{ 0x82,0x51,0x41,0x65,0xF5,0x98,0x5F,0x49 } }; // 1B25DA6E-8AA6-41E9-8251-4165F5985F49
    template <> inline constexpr guid guid_v<winrt::Windows::System::ILauncherViewOptions>{ 0x8A9B29F1,0x7CA7,0x49DE,{ 0x9B,0xD3,0x3C,0x5B,0x71,0x84,0xF6,0x16 } }; // 8A9B29F1-7CA7-49DE-9BD3-3C5B7184F616
    template <> inline constexpr guid guid_v<winrt::Windows::System::IMemoryManagerStatics>{ 0x5C6C279C,0xD7CA,0x4779,{ 0x91,0x88,0x40,0x57,0x21,0x9C,0xE6,0x4C } }; // 5C6C279C-D7CA-4779-9188-4057219CE64C
    template <> inline constexpr guid guid_v<winrt::Windows::System::IMemoryManagerStatics2>{ 0x6EEE351F,0x6D62,0x423F,{ 0x94,0x79,0xB0,0x1F,0x9C,0x9F,0x76,0x69 } }; // 6EEE351F-6D62-423F-9479-B01F9C9F7669
    template <> inline constexpr guid guid_v<winrt::Windows::System::IMemoryManagerStatics3>{ 0x149B59CE,0x92AD,0x4E35,{ 0x89,0xEB,0x50,0xDF,0xB4,0xC0,0xD9,0x1C } }; // 149B59CE-92AD-4E35-89EB-50DFB4C0D91C
    template <> inline constexpr guid guid_v<winrt::Windows::System::IMemoryManagerStatics4>{ 0xC5A94828,0xE84E,0x4886,{ 0x8A,0x0D,0x44,0xB3,0x19,0x0E,0x3B,0x72 } }; // C5A94828-E84E-4886-8A0D-44B3190E3B72
    template <> inline constexpr guid guid_v<winrt::Windows::System::IProcessLauncherOptions>{ 0x3080B9CF,0xF444,0x4A83,{ 0xBE,0xAF,0xA5,0x49,0xA0,0xF3,0x22,0x9C } }; // 3080B9CF-F444-4A83-BEAF-A549A0F3229C
    template <> inline constexpr guid guid_v<winrt::Windows::System::IProcessLauncherResult>{ 0x544C8934,0x86D8,0x4991,{ 0x8E,0x75,0xEC,0xE8,0xA4,0x3B,0x6B,0x6D } }; // 544C8934-86D8-4991-8E75-ECE8A43B6B6D
    template <> inline constexpr guid guid_v<winrt::Windows::System::IProcessLauncherStatics>{ 0x33AB66E7,0x2D0E,0x448B,{ 0xA6,0xA0,0xC1,0x3C,0x38,0x36,0xD0,0x9C } }; // 33AB66E7-2D0E-448B-A6A0-C13C3836D09C
    template <> inline constexpr guid guid_v<winrt::Windows::System::IProcessMemoryReport>{ 0x087305A8,0x9B70,0x4782,{ 0x87,0x41,0x3A,0x98,0x2B,0x6C,0xE5,0xE4 } }; // 087305A8-9B70-4782-8741-3A982B6CE5E4
    template <> inline constexpr guid guid_v<winrt::Windows::System::IProtocolForResultsOperation>{ 0xD581293A,0x6DE9,0x4D28,{ 0x93,0x78,0xF8,0x67,0x82,0xE1,0x82,0xBB } }; // D581293A-6DE9-4D28-9378-F86782E182BB
    template <> inline constexpr guid guid_v<winrt::Windows::System::IRemoteLauncherOptions>{ 0x9E3A2788,0x2891,0x4CDF,{ 0xA2,0xD6,0x9D,0xFF,0x7D,0x02,0xE6,0x93 } }; // 9E3A2788-2891-4CDF-A2D6-9DFF7D02E693
    template <> inline constexpr guid guid_v<winrt::Windows::System::IRemoteLauncherStatics>{ 0xD7DB7A93,0xA30C,0x48B7,{ 0x9F,0x21,0x05,0x10,0x26,0xA4,0xE5,0x17 } }; // D7DB7A93-A30C-48B7-9F21-051026A4E517
    template <> inline constexpr guid guid_v<winrt::Windows::System::IShutdownManagerStatics>{ 0x72E247ED,0xDD5B,0x4D6C,{ 0xB1,0xD0,0xC5,0x7A,0x7B,0xBB,0x5F,0x94 } }; // 72E247ED-DD5B-4D6C-B1D0-C57A7BBB5F94
    template <> inline constexpr guid guid_v<winrt::Windows::System::IShutdownManagerStatics2>{ 0x0F69A02F,0x9C34,0x43C7,{ 0xA8,0xC3,0x70,0xB3,0x0A,0x7F,0x75,0x04 } }; // 0F69A02F-9C34-43C7-A8C3-70B30A7F7504
    template <> inline constexpr guid guid_v<winrt::Windows::System::ITimeZoneSettingsStatics>{ 0x9B3B2BEA,0xA101,0x41AE,{ 0x9F,0xBD,0x02,0x87,0x28,0xBA,0xB7,0x3D } }; // 9B3B2BEA-A101-41AE-9FBD-028728BAB73D
    template <> inline constexpr guid guid_v<winrt::Windows::System::ITimeZoneSettingsStatics2>{ 0x555C0DB8,0x39A8,0x49FA,{ 0xB4,0xF6,0xA2,0xC7,0xFC,0x28,0x42,0xEC } }; // 555C0DB8-39A8-49FA-B4F6-A2C7FC2842EC
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUser>{ 0xDF9A26C6,0xE746,0x4BCD,{ 0xB5,0xD4,0x12,0x01,0x03,0xC4,0x20,0x9B } }; // DF9A26C6-E746-4BCD-B5D4-120103C4209B
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserAuthenticationStatusChangeDeferral>{ 0x88B59568,0xBB30,0x42FB,{ 0xA2,0x70,0xE9,0x90,0x2E,0x40,0xEF,0xA7 } }; // 88B59568-BB30-42FB-A270-E9902E40EFA7
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserAuthenticationStatusChangingEventArgs>{ 0x8C030F28,0xA711,0x4C1E,{ 0xAB,0x48,0x04,0x17,0x9C,0x15,0x93,0x8F } }; // 8C030F28-A711-4C1E-AB48-04179C15938F
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserChangedEventArgs>{ 0x086459DC,0x18C6,0x48DB,{ 0xBC,0x99,0x72,0x4F,0xB9,0x20,0x3C,0xCC } }; // 086459DC-18C6-48DB-BC99-724FB9203CCC
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserChangedEventArgs2>{ 0x6B2CCB44,0x6F01,0x560C,{ 0x97,0xAD,0xFC,0x7F,0x32,0xEC,0x58,0x1F } }; // 6B2CCB44-6F01-560C-97AD-FC7F32EC581F
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserDeviceAssociationChangedEventArgs>{ 0xBD1F6F6C,0xBB5D,0x4D7B,{ 0xA5,0xF0,0xC8,0xCD,0x11,0xA3,0x8D,0x42 } }; // BD1F6F6C-BB5D-4D7B-A5F0-C8CD11A38D42
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserDeviceAssociationStatics>{ 0x7E491E14,0xF85A,0x4C07,{ 0x8D,0xA9,0x7F,0xE3,0xD0,0x54,0x23,0x43 } }; // 7E491E14-F85A-4C07-8DA9-7FE3D0542343
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserPicker>{ 0x7D548008,0xF1E3,0x4A6C,{ 0x8D,0xDC,0xA9,0xBB,0x0F,0x48,0x8A,0xED } }; // 7D548008-F1E3-4A6C-8DDC-A9BB0F488AED
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserPickerStatics>{ 0xDE3290DC,0x7E73,0x4DF6,{ 0xA1,0xAE,0x4D,0x7E,0xCA,0x82,0xB4,0x0D } }; // DE3290DC-7E73-4DF6-A1AE-4D7ECA82B40D
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserStatics>{ 0x155EB23B,0x242A,0x45E0,{ 0xA2,0xE9,0x31,0x71,0xFC,0x6A,0x7F,0xDD } }; // 155EB23B-242A-45E0-A2E9-3171FC6A7FDD
    template <> inline constexpr guid guid_v<winrt::Windows::System::IUserWatcher>{ 0x155EB23B,0x242A,0x45E0,{ 0xA2,0xE9,0x31,0x71,0xFC,0x6A,0x7F,0xBB } }; // 155EB23B-242A-45E0-A2E9-3171FC6A7FBB
    template <> inline constexpr guid guid_v<winrt::Windows::System::DispatcherQueueHandler>{ 0xDFA2DC9C,0x1A2D,0x4917,{ 0x98,0xF2,0x93,0x9A,0xF1,0xD6,0xE0,0xC8 } }; // DFA2DC9C-1A2D-4917-98F2-939AF1D6E0C8
    template <> struct default_interface<winrt::Windows::System::AppActivationResult>{ using type = winrt::Windows::System::IAppActivationResult; };
    template <> struct default_interface<winrt::Windows::System::AppDiagnosticInfo>{ using type = winrt::Windows::System::IAppDiagnosticInfo; };
    template <> struct default_interface<winrt::Windows::System::AppDiagnosticInfoWatcher>{ using type = winrt::Windows::System::IAppDiagnosticInfoWatcher; };
    template <> struct default_interface<winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs>{ using type = winrt::Windows::System::IAppDiagnosticInfoWatcherEventArgs; };
    template <> struct default_interface<winrt::Windows::System::AppExecutionStateChangeResult>{ using type = winrt::Windows::System::IAppExecutionStateChangeResult; };
    template <> struct default_interface<winrt::Windows::System::AppMemoryReport>{ using type = winrt::Windows::System::IAppMemoryReport; };
    template <> struct default_interface<winrt::Windows::System::AppMemoryUsageLimitChangingEventArgs>{ using type = winrt::Windows::System::IAppMemoryUsageLimitChangingEventArgs; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupBackgroundTaskReport>{ using type = winrt::Windows::System::IAppResourceGroupBackgroundTaskReport; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupInfo>{ using type = winrt::Windows::System::IAppResourceGroupInfo; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupInfoWatcher>{ using type = winrt::Windows::System::IAppResourceGroupInfoWatcher; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs>{ using type = winrt::Windows::System::IAppResourceGroupInfoWatcherEventArgs; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupInfoWatcherExecutionStateChangedEventArgs>{ using type = winrt::Windows::System::IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupMemoryReport>{ using type = winrt::Windows::System::IAppResourceGroupMemoryReport; };
    template <> struct default_interface<winrt::Windows::System::AppResourceGroupStateReport>{ using type = winrt::Windows::System::IAppResourceGroupStateReport; };
    template <> struct default_interface<winrt::Windows::System::AppUriHandlerHost>{ using type = winrt::Windows::System::IAppUriHandlerHost; };
    template <> struct default_interface<winrt::Windows::System::AppUriHandlerRegistration>{ using type = winrt::Windows::System::IAppUriHandlerRegistration; };
    template <> struct default_interface<winrt::Windows::System::AppUriHandlerRegistrationManager>{ using type = winrt::Windows::System::IAppUriHandlerRegistrationManager; };
    template <> struct default_interface<winrt::Windows::System::DispatcherQueue>{ using type = winrt::Windows::System::IDispatcherQueue; };
    template <> struct default_interface<winrt::Windows::System::DispatcherQueueController>{ using type = winrt::Windows::System::IDispatcherQueueController; };
    template <> struct default_interface<winrt::Windows::System::DispatcherQueueShutdownStartingEventArgs>{ using type = winrt::Windows::System::IDispatcherQueueShutdownStartingEventArgs; };
    template <> struct default_interface<winrt::Windows::System::DispatcherQueueTimer>{ using type = winrt::Windows::System::IDispatcherQueueTimer; };
    template <> struct default_interface<winrt::Windows::System::FolderLauncherOptions>{ using type = winrt::Windows::System::IFolderLauncherOptions; };
    template <> struct default_interface<winrt::Windows::System::LaunchUriResult>{ using type = winrt::Windows::System::ILaunchUriResult; };
    template <> struct default_interface<winrt::Windows::System::LauncherOptions>{ using type = winrt::Windows::System::ILauncherOptions; };
    template <> struct default_interface<winrt::Windows::System::LauncherUIOptions>{ using type = winrt::Windows::System::ILauncherUIOptions; };
    template <> struct default_interface<winrt::Windows::System::ProcessLauncherOptions>{ using type = winrt::Windows::System::IProcessLauncherOptions; };
    template <> struct default_interface<winrt::Windows::System::ProcessLauncherResult>{ using type = winrt::Windows::System::IProcessLauncherResult; };
    template <> struct default_interface<winrt::Windows::System::ProcessMemoryReport>{ using type = winrt::Windows::System::IProcessMemoryReport; };
    template <> struct default_interface<winrt::Windows::System::ProtocolForResultsOperation>{ using type = winrt::Windows::System::IProtocolForResultsOperation; };
    template <> struct default_interface<winrt::Windows::System::RemoteLauncherOptions>{ using type = winrt::Windows::System::IRemoteLauncherOptions; };
    template <> struct default_interface<winrt::Windows::System::User>{ using type = winrt::Windows::System::IUser; };
    template <> struct default_interface<winrt::Windows::System::UserAuthenticationStatusChangeDeferral>{ using type = winrt::Windows::System::IUserAuthenticationStatusChangeDeferral; };
    template <> struct default_interface<winrt::Windows::System::UserAuthenticationStatusChangingEventArgs>{ using type = winrt::Windows::System::IUserAuthenticationStatusChangingEventArgs; };
    template <> struct default_interface<winrt::Windows::System::UserChangedEventArgs>{ using type = winrt::Windows::System::IUserChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::System::UserDeviceAssociationChangedEventArgs>{ using type = winrt::Windows::System::IUserDeviceAssociationChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::System::UserPicker>{ using type = winrt::Windows::System::IUserPicker; };
    template <> struct default_interface<winrt::Windows::System::UserWatcher>{ using type = winrt::Windows::System::IUserWatcher; };
    template <> struct abi<winrt::Windows::System::IAppActivationResult>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExtendedError(winrt::hresult*) noexcept = 0;
            virtual int32_t __stdcall get_AppResourceGroupInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfo>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AppInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfo2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetResourceGroups(void**) noexcept = 0;
            virtual int32_t __stdcall CreateResourceGroupWatcher(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfo3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall LaunchAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfoStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall RequestInfoAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfoStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateWatcher(void**) noexcept = 0;
            virtual int32_t __stdcall RequestAccessAsync(void**) noexcept = 0;
            virtual int32_t __stdcall RequestInfoForPackageAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall RequestInfoForAppAsync(void**) noexcept = 0;
            virtual int32_t __stdcall RequestInfoForAppUserModelId(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfoWatcher>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall add_Added(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Added(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Removed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Removed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_EnumerationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_EnumerationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Stopped(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Stopped(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppDiagnosticInfoWatcherEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AppDiagnosticInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppExecutionStateChangeResult>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExtendedError(winrt::hresult*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppMemoryReport>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_PrivateCommitUsage(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_PeakPrivateCommitUsage(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_TotalCommitUsage(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_TotalCommitLimit(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppMemoryReport2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExpectedTotalCommitLimit(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppMemoryUsageLimitChangingEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_OldLimit(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_NewLimit(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupBackgroundTaskReport>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_TaskId(winrt::guid*) noexcept = 0;
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Trigger(void**) noexcept = 0;
            virtual int32_t __stdcall get_EntryPoint(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupInfo>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_InstanceId(winrt::guid*) noexcept = 0;
            virtual int32_t __stdcall get_IsShared(bool*) noexcept = 0;
            virtual int32_t __stdcall GetBackgroundTaskReports(void**) noexcept = 0;
            virtual int32_t __stdcall GetMemoryReport(void**) noexcept = 0;
            virtual int32_t __stdcall GetProcessDiagnosticInfos(void**) noexcept = 0;
            virtual int32_t __stdcall GetStateReport(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupInfo2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall StartSuspendAsync(void**) noexcept = 0;
            virtual int32_t __stdcall StartResumeAsync(void**) noexcept = 0;
            virtual int32_t __stdcall StartTerminateAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupInfoWatcher>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall add_Added(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Added(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Removed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Removed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_EnumerationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_EnumerationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Stopped(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Stopped(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ExecutionStateChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ExecutionStateChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupInfoWatcherEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AppDiagnosticInfos(void**) noexcept = 0;
            virtual int32_t __stdcall get_AppResourceGroupInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AppDiagnosticInfos(void**) noexcept = 0;
            virtual int32_t __stdcall get_AppResourceGroupInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupMemoryReport>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_CommitUsageLimit(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_CommitUsageLevel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_PrivateCommitUsage(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_TotalCommitUsage(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppResourceGroupStateReport>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExecutionState(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_EnergyQuotaState(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppUriHandlerHost>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall put_Name(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppUriHandlerHostFactory>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppUriHandlerRegistration>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_User(void**) noexcept = 0;
            virtual int32_t __stdcall GetAppAddedHostsAsync(void**) noexcept = 0;
            virtual int32_t __stdcall SetAppAddedHostsAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppUriHandlerRegistrationManager>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_User(void**) noexcept = 0;
            virtual int32_t __stdcall TryGetRegistration(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IAppUriHandlerRegistrationManagerStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
            virtual int32_t __stdcall GetForUser(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDateTimeSettingsStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall SetSystemDateTime(int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueue>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateTimer(void**) noexcept = 0;
            virtual int32_t __stdcall TryEnqueue(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall TryEnqueueWithPriority(int32_t, void*, bool*) noexcept = 0;
            virtual int32_t __stdcall add_ShutdownStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ShutdownStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ShutdownCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ShutdownCompleted(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueue2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_HasThreadAccess(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueueController>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_DispatcherQueue(void**) noexcept = 0;
            virtual int32_t __stdcall ShutdownQueueAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueueControllerStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateOnDedicatedThread(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueueShutdownStartingEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueueStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetForCurrentThread(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IDispatcherQueueTimer>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Interval(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Interval(int64_t) noexcept = 0;
            virtual int32_t __stdcall get_IsRunning(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsRepeating(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsRepeating(bool) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
            virtual int32_t __stdcall add_Tick(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Tick(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IFolderLauncherOptions>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ItemsToSelect(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IKnownUserPropertiesStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_DisplayName(void**) noexcept = 0;
            virtual int32_t __stdcall get_FirstName(void**) noexcept = 0;
            virtual int32_t __stdcall get_LastName(void**) noexcept = 0;
            virtual int32_t __stdcall get_ProviderName(void**) noexcept = 0;
            virtual int32_t __stdcall get_AccountName(void**) noexcept = 0;
            virtual int32_t __stdcall get_GuestHost(void**) noexcept = 0;
            virtual int32_t __stdcall get_PrincipalName(void**) noexcept = 0;
            virtual int32_t __stdcall get_DomainName(void**) noexcept = 0;
            virtual int32_t __stdcall get_SessionInitiationProtocolUri(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILaunchUriResult>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Result(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherOptions>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_TreatAsUntrusted(bool*) noexcept = 0;
            virtual int32_t __stdcall put_TreatAsUntrusted(bool) noexcept = 0;
            virtual int32_t __stdcall get_DisplayApplicationPicker(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DisplayApplicationPicker(bool) noexcept = 0;
            virtual int32_t __stdcall get_UI(void**) noexcept = 0;
            virtual int32_t __stdcall get_PreferredApplicationPackageFamilyName(void**) noexcept = 0;
            virtual int32_t __stdcall put_PreferredApplicationPackageFamilyName(void*) noexcept = 0;
            virtual int32_t __stdcall get_PreferredApplicationDisplayName(void**) noexcept = 0;
            virtual int32_t __stdcall put_PreferredApplicationDisplayName(void*) noexcept = 0;
            virtual int32_t __stdcall get_FallbackUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_FallbackUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_ContentType(void**) noexcept = 0;
            virtual int32_t __stdcall put_ContentType(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherOptions2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_TargetApplicationPackageFamilyName(void**) noexcept = 0;
            virtual int32_t __stdcall put_TargetApplicationPackageFamilyName(void*) noexcept = 0;
            virtual int32_t __stdcall get_NeighboringFilesQuery(void**) noexcept = 0;
            virtual int32_t __stdcall put_NeighboringFilesQuery(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherOptions3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_IgnoreAppUriHandlers(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IgnoreAppUriHandlers(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherOptions4>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_LimitPickerToCurrentAppAndAppUriHandlers(bool*) noexcept = 0;
            virtual int32_t __stdcall put_LimitPickerToCurrentAppAndAppUriHandlers(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall LaunchFileAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchFileWithOptionsAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriWithOptionsAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall LaunchUriForResultsAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriForResultsWithDataAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriWithDataAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall QueryUriSupportAsync(void*, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall QueryUriSupportWithPackageFamilyNameAsync(void*, int32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall QueryFileSupportAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall QueryFileSupportWithPackageFamilyNameAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindUriSchemeHandlersAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindUriSchemeHandlersWithLaunchUriTypeAsync(void*, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindFileHandlersAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherStatics3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall LaunchFolderAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchFolderWithOptionsAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherStatics4>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall QueryAppUriSupportAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall QueryAppUriSupportWithPackageFamilyNameAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindAppUriHandlersAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriForUserAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriWithOptionsForUserAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriWithDataForUserAsync(void*, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriForResultsForUserAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriForResultsWithDataForUserAsync(void*, void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherStatics5>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall LaunchFolderPathAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchFolderPathWithOptionsAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchFolderPathForUserAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchFolderPathWithOptionsForUserAsync(void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherUIOptions>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_InvocationPoint(void**) noexcept = 0;
            virtual int32_t __stdcall put_InvocationPoint(void*) noexcept = 0;
            virtual int32_t __stdcall get_SelectionRect(void**) noexcept = 0;
            virtual int32_t __stdcall put_SelectionRect(void*) noexcept = 0;
            virtual int32_t __stdcall get_PreferredPlacement(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PreferredPlacement(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ILauncherViewOptions>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_DesiredRemainingView(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_DesiredRemainingView(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IMemoryManagerStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AppMemoryUsage(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_AppMemoryUsageLimit(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_AppMemoryUsageLevel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall add_AppMemoryUsageIncreased(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AppMemoryUsageIncreased(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AppMemoryUsageDecreased(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AppMemoryUsageDecreased(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AppMemoryUsageLimitChanging(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AppMemoryUsageLimitChanging(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IMemoryManagerStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetAppMemoryReport(void**) noexcept = 0;
            virtual int32_t __stdcall GetProcessMemoryReport(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IMemoryManagerStatics3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall TrySetAppMemoryUsageLimit(uint64_t, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IMemoryManagerStatics4>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExpectedAppMemoryUsageLimit(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IProcessLauncherOptions>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_StandardInput(void**) noexcept = 0;
            virtual int32_t __stdcall put_StandardInput(void*) noexcept = 0;
            virtual int32_t __stdcall get_StandardOutput(void**) noexcept = 0;
            virtual int32_t __stdcall put_StandardOutput(void*) noexcept = 0;
            virtual int32_t __stdcall get_StandardError(void**) noexcept = 0;
            virtual int32_t __stdcall put_StandardError(void*) noexcept = 0;
            virtual int32_t __stdcall get_WorkingDirectory(void**) noexcept = 0;
            virtual int32_t __stdcall put_WorkingDirectory(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IProcessLauncherResult>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExitCode(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IProcessLauncherStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall RunToCompletionAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RunToCompletionAsyncWithOptions(void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IProcessMemoryReport>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_PrivateWorkingSetUsage(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_TotalWorkingSetUsage(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IProtocolForResultsOperation>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall ReportCompleted(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IRemoteLauncherOptions>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_FallbackUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_FallbackUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_PreferredAppIds(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IRemoteLauncherStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall LaunchUriAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriWithOptionsAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall LaunchUriWithDataAsync(void*, void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IShutdownManagerStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall BeginShutdown(int32_t, int64_t) noexcept = 0;
            virtual int32_t __stdcall CancelShutdown() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IShutdownManagerStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall IsPowerStateSupported(int32_t, bool*) noexcept = 0;
            virtual int32_t __stdcall EnterPowerState(int32_t) noexcept = 0;
            virtual int32_t __stdcall EnterPowerStateWithTimeSpan(int32_t, int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ITimeZoneSettingsStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_CurrentTimeZoneDisplayName(void**) noexcept = 0;
            virtual int32_t __stdcall get_SupportedTimeZoneDisplayNames(void**) noexcept = 0;
            virtual int32_t __stdcall get_CanChangeTimeZone(bool*) noexcept = 0;
            virtual int32_t __stdcall ChangeTimeZoneByDisplayName(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::ITimeZoneSettingsStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall AutoUpdateTimeZoneAsync(int64_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUser>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_NonRoamableId(void**) noexcept = 0;
            virtual int32_t __stdcall get_AuthenticationStatus(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Type(int32_t*) noexcept = 0;
            virtual int32_t __stdcall GetPropertyAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetPropertiesAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetPictureAsync(int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserAuthenticationStatusChangeDeferral>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall Complete() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserAuthenticationStatusChangingEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
            virtual int32_t __stdcall get_User(void**) noexcept = 0;
            virtual int32_t __stdcall get_NewStatus(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_CurrentStatus(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserChangedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_User(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserChangedEventArgs2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ChangedPropertyKinds(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserDeviceAssociationChangedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_DeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_NewUser(void**) noexcept = 0;
            virtual int32_t __stdcall get_OldUser(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserDeviceAssociationStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall FindUserFromDeviceId(void*, void**) noexcept = 0;
            virtual int32_t __stdcall add_UserDeviceAssociationChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_UserDeviceAssociationChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserPicker>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AllowGuestAccounts(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowGuestAccounts(bool) noexcept = 0;
            virtual int32_t __stdcall get_SuggestedSelectedUser(void**) noexcept = 0;
            virtual int32_t __stdcall put_SuggestedSelectedUser(void*) noexcept = 0;
            virtual int32_t __stdcall PickSingleUserAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserPickerStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall IsSupported(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateWatcher(void**) noexcept = 0;
            virtual int32_t __stdcall FindAllAsync(void**) noexcept = 0;
            virtual int32_t __stdcall FindAllAsyncByType(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindAllAsyncByTypeAndStatus(int32_t, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall GetFromId(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::IUserWatcher>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
            virtual int32_t __stdcall add_Added(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Added(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Removed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Removed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Updated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Updated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AuthenticationStatusChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AuthenticationStatusChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AuthenticationStatusChanging(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AuthenticationStatusChanging(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_EnumerationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_EnumerationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Stopped(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Stopped(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::System::DispatcherQueueHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke() noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_System_IAppActivationResult
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::hresult) ExtendedError() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupInfo) AppResourceGroupInfo() const;
    };
    template <> struct consume<winrt::Windows::System::IAppActivationResult>
    {
        template <typename D> using type = consume_Windows_System_IAppActivationResult<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfo
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::ApplicationModel::AppInfo) AppInfo() const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfo>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfo<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfo2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppResourceGroupInfo>) GetResourceGroups() const;
        WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupInfoWatcher) CreateResourceGroupWatcher() const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfo2>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfo2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfo3
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::AppActivationResult>) LaunchAsync() const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfo3>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfo3<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfoStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppDiagnosticInfo>>) RequestInfoAsync() const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfoStatics>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfoStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfoStatics2
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::AppDiagnosticInfoWatcher) CreateWatcher() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::DiagnosticAccessStatus>) RequestAccessAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppDiagnosticInfo>>) RequestInfoForPackageAsync(param::hstring const& packageFamilyName) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppDiagnosticInfo>>) RequestInfoForAppAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppDiagnosticInfo>>) RequestInfoForAppAsync(param::hstring const& appUserModelId) const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfoStatics2>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfoStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfoWatcher
    {
        WINRT_IMPL_AUTO(winrt::event_token) Added(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs> const& handler) const;
        using Added_revoker = impl::event_revoker<winrt::Windows::System::IAppDiagnosticInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppDiagnosticInfoWatcher>::remove_Added>;
        [[nodiscard]] Added_revoker Added(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Added(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Removed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs> const& handler) const;
        using Removed_revoker = impl::event_revoker<winrt::Windows::System::IAppDiagnosticInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppDiagnosticInfoWatcher>::remove_Removed>;
        [[nodiscard]] Removed_revoker Removed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::System::AppDiagnosticInfoWatcherEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Removed(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) EnumerationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using EnumerationCompleted_revoker = impl::event_revoker<winrt::Windows::System::IAppDiagnosticInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppDiagnosticInfoWatcher>::remove_EnumerationCompleted>;
        [[nodiscard]] EnumerationCompleted_revoker EnumerationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) EnumerationCompleted(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Stopped(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Stopped_revoker = impl::event_revoker<winrt::Windows::System::IAppDiagnosticInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppDiagnosticInfoWatcher>::remove_Stopped>;
        [[nodiscard]] Stopped_revoker Stopped(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppDiagnosticInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) Stopped(winrt::event_token const& token) const noexcept;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppDiagnosticInfoWatcherStatus) Status() const;
        WINRT_IMPL_AUTO(void) Start() const;
        WINRT_IMPL_AUTO(void) Stop() const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfoWatcher>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfoWatcher<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppDiagnosticInfoWatcherEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppDiagnosticInfo) AppDiagnosticInfo() const;
    };
    template <> struct consume<winrt::Windows::System::IAppDiagnosticInfoWatcherEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IAppDiagnosticInfoWatcherEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppExecutionStateChangeResult
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::hresult) ExtendedError() const;
    };
    template <> struct consume<winrt::Windows::System::IAppExecutionStateChangeResult>
    {
        template <typename D> using type = consume_Windows_System_IAppExecutionStateChangeResult<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppMemoryReport
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) PrivateCommitUsage() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) PeakPrivateCommitUsage() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) TotalCommitUsage() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) TotalCommitLimit() const;
    };
    template <> struct consume<winrt::Windows::System::IAppMemoryReport>
    {
        template <typename D> using type = consume_Windows_System_IAppMemoryReport<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppMemoryReport2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) ExpectedTotalCommitLimit() const;
    };
    template <> struct consume<winrt::Windows::System::IAppMemoryReport2>
    {
        template <typename D> using type = consume_Windows_System_IAppMemoryReport2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppMemoryUsageLimitChangingEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) OldLimit() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) NewLimit() const;
    };
    template <> struct consume<winrt::Windows::System::IAppMemoryUsageLimitChangingEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IAppMemoryUsageLimitChangingEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupBackgroundTaskReport
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::guid) TaskId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Name() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Trigger() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) EntryPoint() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupBackgroundTaskReport>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupBackgroundTaskReport<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupInfo
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::guid) InstanceId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsShared() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppResourceGroupBackgroundTaskReport>) GetBackgroundTaskReports() const;
        WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupMemoryReport) GetMemoryReport() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::Diagnostics::ProcessDiagnosticInfo>) GetProcessDiagnosticInfos() const;
        WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupStateReport) GetStateReport() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupInfo>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupInfo<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupInfo2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::AppExecutionStateChangeResult>) StartSuspendAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::AppExecutionStateChangeResult>) StartResumeAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::AppExecutionStateChangeResult>) StartTerminateAsync() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupInfo2>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupInfo2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupInfoWatcher
    {
        WINRT_IMPL_AUTO(winrt::event_token) Added(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs> const& handler) const;
        using Added_revoker = impl::event_revoker<winrt::Windows::System::IAppResourceGroupInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppResourceGroupInfoWatcher>::remove_Added>;
        [[nodiscard]] Added_revoker Added(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Added(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Removed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs> const& handler) const;
        using Removed_revoker = impl::event_revoker<winrt::Windows::System::IAppResourceGroupInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppResourceGroupInfoWatcher>::remove_Removed>;
        [[nodiscard]] Removed_revoker Removed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::System::AppResourceGroupInfoWatcherEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Removed(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) EnumerationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using EnumerationCompleted_revoker = impl::event_revoker<winrt::Windows::System::IAppResourceGroupInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppResourceGroupInfoWatcher>::remove_EnumerationCompleted>;
        [[nodiscard]] EnumerationCompleted_revoker EnumerationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) EnumerationCompleted(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Stopped(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Stopped_revoker = impl::event_revoker<winrt::Windows::System::IAppResourceGroupInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppResourceGroupInfoWatcher>::remove_Stopped>;
        [[nodiscard]] Stopped_revoker Stopped(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) Stopped(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) ExecutionStateChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::System::AppResourceGroupInfoWatcherExecutionStateChangedEventArgs> const& handler) const;
        using ExecutionStateChanged_revoker = impl::event_revoker<winrt::Windows::System::IAppResourceGroupInfoWatcher, &impl::abi_t<winrt::Windows::System::IAppResourceGroupInfoWatcher>::remove_ExecutionStateChanged>;
        [[nodiscard]] ExecutionStateChanged_revoker ExecutionStateChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::AppResourceGroupInfoWatcher, winrt::Windows::System::AppResourceGroupInfoWatcherExecutionStateChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) ExecutionStateChanged(winrt::event_token const& token) const noexcept;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupInfoWatcherStatus) Status() const;
        WINRT_IMPL_AUTO(void) Start() const;
        WINRT_IMPL_AUTO(void) Stop() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupInfoWatcher>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupInfoWatcher<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupInfoWatcherEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::System::AppDiagnosticInfo>) AppDiagnosticInfos() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupInfo) AppResourceGroupInfo() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupInfoWatcherEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupInfoWatcherEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::System::AppDiagnosticInfo>) AppDiagnosticInfos() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupInfo) AppResourceGroupInfo() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupMemoryReport
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) CommitUsageLimit() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppMemoryUsageLevel) CommitUsageLevel() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) PrivateCommitUsage() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) TotalCommitUsage() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupMemoryReport>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupMemoryReport<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppResourceGroupStateReport
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupExecutionState) ExecutionState() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppResourceGroupEnergyQuotaState) EnergyQuotaState() const;
    };
    template <> struct consume<winrt::Windows::System::IAppResourceGroupStateReport>
    {
        template <typename D> using type = consume_Windows_System_IAppResourceGroupStateReport<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppUriHandlerHost
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Name() const;
        WINRT_IMPL_AUTO(void) Name(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Windows::System::IAppUriHandlerHost>
    {
        template <typename D> using type = consume_Windows_System_IAppUriHandlerHost<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppUriHandlerHostFactory
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::AppUriHandlerHost) CreateInstance(param::hstring const& name) const;
    };
    template <> struct consume<winrt::Windows::System::IAppUriHandlerHostFactory>
    {
        template <typename D> using type = consume_Windows_System_IAppUriHandlerHostFactory<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppUriHandlerRegistration
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Name() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) User() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVector<winrt::Windows::System::AppUriHandlerHost>>) GetAppAddedHostsAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) SetAppAddedHostsAsync(param::async_iterable<winrt::Windows::System::AppUriHandlerHost> const& hosts) const;
    };
    template <> struct consume<winrt::Windows::System::IAppUriHandlerRegistration>
    {
        template <typename D> using type = consume_Windows_System_IAppUriHandlerRegistration<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppUriHandlerRegistrationManager
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) User() const;
        WINRT_IMPL_AUTO(winrt::Windows::System::AppUriHandlerRegistration) TryGetRegistration(param::hstring const& name) const;
    };
    template <> struct consume<winrt::Windows::System::IAppUriHandlerRegistrationManager>
    {
        template <typename D> using type = consume_Windows_System_IAppUriHandlerRegistrationManager<D>;
    };
    template <typename D>
    struct consume_Windows_System_IAppUriHandlerRegistrationManagerStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::AppUriHandlerRegistrationManager) GetDefault() const;
        WINRT_IMPL_AUTO(winrt::Windows::System::AppUriHandlerRegistrationManager) GetForUser(winrt::Windows::System::User const& user) const;
    };
    template <> struct consume<winrt::Windows::System::IAppUriHandlerRegistrationManagerStatics>
    {
        template <typename D> using type = consume_Windows_System_IAppUriHandlerRegistrationManagerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDateTimeSettingsStatics
    {
        WINRT_IMPL_AUTO(void) SetSystemDateTime(winrt::Windows::Foundation::DateTime const& utcDateTime) const;
    };
    template <> struct consume<winrt::Windows::System::IDateTimeSettingsStatics>
    {
        template <typename D> using type = consume_Windows_System_IDateTimeSettingsStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueue
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::DispatcherQueueTimer) CreateTimer() const;
        WINRT_IMPL_AUTO(bool) TryEnqueue(winrt::Windows::System::DispatcherQueueHandler const& callback) const;
        WINRT_IMPL_AUTO(bool) TryEnqueue(winrt::Windows::System::DispatcherQueuePriority const& priority, winrt::Windows::System::DispatcherQueueHandler const& callback) const;
        WINRT_IMPL_AUTO(winrt::event_token) ShutdownStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::DispatcherQueue, winrt::Windows::System::DispatcherQueueShutdownStartingEventArgs> const& handler) const;
        using ShutdownStarting_revoker = impl::event_revoker<winrt::Windows::System::IDispatcherQueue, &impl::abi_t<winrt::Windows::System::IDispatcherQueue>::remove_ShutdownStarting>;
        [[nodiscard]] ShutdownStarting_revoker ShutdownStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::DispatcherQueue, winrt::Windows::System::DispatcherQueueShutdownStartingEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) ShutdownStarting(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) ShutdownCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::DispatcherQueue, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using ShutdownCompleted_revoker = impl::event_revoker<winrt::Windows::System::IDispatcherQueue, &impl::abi_t<winrt::Windows::System::IDispatcherQueue>::remove_ShutdownCompleted>;
        [[nodiscard]] ShutdownCompleted_revoker ShutdownCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::DispatcherQueue, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) ShutdownCompleted(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueue>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueue<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueue2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) HasThreadAccess() const;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueue2>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueue2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueueController
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::DispatcherQueue) DispatcherQueue() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) ShutdownQueueAsync() const;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueueController>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueueController<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueueControllerStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::DispatcherQueueController) CreateOnDedicatedThread() const;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueueControllerStatics>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueueControllerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueueShutdownStartingEventArgs
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Deferral) GetDeferral() const;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueueShutdownStartingEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueueShutdownStartingEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueueStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::DispatcherQueue) GetForCurrentThread() const;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueueStatics>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueueStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IDispatcherQueueTimer
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::TimeSpan) Interval() const;
        WINRT_IMPL_AUTO(void) Interval(winrt::Windows::Foundation::TimeSpan const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsRunning() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsRepeating() const;
        WINRT_IMPL_AUTO(void) IsRepeating(bool value) const;
        WINRT_IMPL_AUTO(void) Start() const;
        WINRT_IMPL_AUTO(void) Stop() const;
        WINRT_IMPL_AUTO(winrt::event_token) Tick(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::DispatcherQueueTimer, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Tick_revoker = impl::event_revoker<winrt::Windows::System::IDispatcherQueueTimer, &impl::abi_t<winrt::Windows::System::IDispatcherQueueTimer>::remove_Tick>;
        [[nodiscard]] Tick_revoker Tick(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::DispatcherQueueTimer, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) Tick(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::System::IDispatcherQueueTimer>
    {
        template <typename D> using type = consume_Windows_System_IDispatcherQueueTimer<D>;
    };
    template <typename D>
    struct consume_Windows_System_IFolderLauncherOptions
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVector<winrt::Windows::Storage::IStorageItem>) ItemsToSelect() const;
    };
    template <> struct consume<winrt::Windows::System::IFolderLauncherOptions>
    {
        template <typename D> using type = consume_Windows_System_IFolderLauncherOptions<D>;
    };
    template <typename D>
    struct consume_Windows_System_IKnownUserPropertiesStatics
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) DisplayName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) FirstName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) LastName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) ProviderName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) AccountName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) GuestHost() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) PrincipalName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) DomainName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) SessionInitiationProtocolUri() const;
    };
    template <> struct consume<winrt::Windows::System::IKnownUserPropertiesStatics>
    {
        template <typename D> using type = consume_Windows_System_IKnownUserPropertiesStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILaunchUriResult
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::LaunchUriStatus) Status() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::ValueSet) Result() const;
    };
    template <> struct consume<winrt::Windows::System::ILaunchUriResult>
    {
        template <typename D> using type = consume_Windows_System_ILaunchUriResult<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherOptions
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) TreatAsUntrusted() const;
        WINRT_IMPL_AUTO(void) TreatAsUntrusted(bool value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) DisplayApplicationPicker() const;
        WINRT_IMPL_AUTO(void) DisplayApplicationPicker(bool value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::LauncherUIOptions) UI() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) PreferredApplicationPackageFamilyName() const;
        WINRT_IMPL_AUTO(void) PreferredApplicationPackageFamilyName(param::hstring const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) PreferredApplicationDisplayName() const;
        WINRT_IMPL_AUTO(void) PreferredApplicationDisplayName(param::hstring const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Uri) FallbackUri() const;
        WINRT_IMPL_AUTO(void) FallbackUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) ContentType() const;
        WINRT_IMPL_AUTO(void) ContentType(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherOptions>
    {
        template <typename D> using type = consume_Windows_System_ILauncherOptions<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherOptions2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) TargetApplicationPackageFamilyName() const;
        WINRT_IMPL_AUTO(void) TargetApplicationPackageFamilyName(param::hstring const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Storage::Search::StorageFileQueryResult) NeighboringFilesQuery() const;
        WINRT_IMPL_AUTO(void) NeighboringFilesQuery(winrt::Windows::Storage::Search::StorageFileQueryResult const& value) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherOptions2>
    {
        template <typename D> using type = consume_Windows_System_ILauncherOptions2<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherOptions3
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IgnoreAppUriHandlers() const;
        WINRT_IMPL_AUTO(void) IgnoreAppUriHandlers(bool value) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherOptions3>
    {
        template <typename D> using type = consume_Windows_System_ILauncherOptions3<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherOptions4
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) LimitPickerToCurrentAppAndAppUriHandlers() const;
        WINRT_IMPL_AUTO(void) LimitPickerToCurrentAppAndAppUriHandlers(bool value) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherOptions4>
    {
        template <typename D> using type = consume_Windows_System_ILauncherOptions4<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFileAsync(winrt::Windows::Storage::IStorageFile const& file) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFileAsync(winrt::Windows::Storage::IStorageFile const& file, winrt::Windows::System::LauncherOptions const& options) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchUriAsync(winrt::Windows::Foundation::Uri const& uri) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchUriAsync(winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherStatics>
    {
        template <typename D> using type = consume_Windows_System_ILauncherStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherStatics2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriResult>) LaunchUriForResultsAsync(winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriResult>) LaunchUriForResultsAsync(winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options, winrt::Windows::Foundation::Collections::ValueSet const& inputData) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchUriAsync(winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options, winrt::Windows::Foundation::Collections::ValueSet const& inputData) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchQuerySupportStatus>) QueryUriSupportAsync(winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LaunchQuerySupportType const& launchQuerySupportType) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchQuerySupportStatus>) QueryUriSupportAsync(winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LaunchQuerySupportType const& launchQuerySupportType, param::hstring const& packageFamilyName) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchQuerySupportStatus>) QueryFileSupportAsync(winrt::Windows::Storage::StorageFile const& file) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchQuerySupportStatus>) QueryFileSupportAsync(winrt::Windows::Storage::StorageFile const& file, param::hstring const& packageFamilyName) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::ApplicationModel::AppInfo>>) FindUriSchemeHandlersAsync(param::hstring const& scheme) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::ApplicationModel::AppInfo>>) FindUriSchemeHandlersAsync(param::hstring const& scheme, winrt::Windows::System::LaunchQuerySupportType const& launchQuerySupportType) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::ApplicationModel::AppInfo>>) FindFileHandlersAsync(param::hstring const& extension) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherStatics2>
    {
        template <typename D> using type = consume_Windows_System_ILauncherStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherStatics3
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFolderAsync(winrt::Windows::Storage::IStorageFolder const& folder) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFolderAsync(winrt::Windows::Storage::IStorageFolder const& folder, winrt::Windows::System::FolderLauncherOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherStatics3>
    {
        template <typename D> using type = consume_Windows_System_ILauncherStatics3<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherStatics4
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchQuerySupportStatus>) QueryAppUriSupportAsync(winrt::Windows::Foundation::Uri const& uri) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchQuerySupportStatus>) QueryAppUriSupportAsync(winrt::Windows::Foundation::Uri const& uri, param::hstring const& packageFamilyName) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::ApplicationModel::AppInfo>>) FindAppUriHandlersAsync(winrt::Windows::Foundation::Uri const& uri) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriStatus>) LaunchUriForUserAsync(winrt::Windows::System::User const& user, winrt::Windows::Foundation::Uri const& uri) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriStatus>) LaunchUriForUserAsync(winrt::Windows::System::User const& user, winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriStatus>) LaunchUriForUserAsync(winrt::Windows::System::User const& user, winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options, winrt::Windows::Foundation::Collections::ValueSet const& inputData) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriResult>) LaunchUriForResultsForUserAsync(winrt::Windows::System::User const& user, winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::LaunchUriResult>) LaunchUriForResultsForUserAsync(winrt::Windows::System::User const& user, winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::LauncherOptions const& options, winrt::Windows::Foundation::Collections::ValueSet const& inputData) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherStatics4>
    {
        template <typename D> using type = consume_Windows_System_ILauncherStatics4<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherStatics5
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFolderPathAsync(param::hstring const& path) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFolderPathAsync(param::hstring const& path, winrt::Windows::System::FolderLauncherOptions const& options) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFolderPathForUserAsync(winrt::Windows::System::User const& user, param::hstring const& path) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<bool>) LaunchFolderPathForUserAsync(winrt::Windows::System::User const& user, param::hstring const& path, winrt::Windows::System::FolderLauncherOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherStatics5>
    {
        template <typename D> using type = consume_Windows_System_ILauncherStatics5<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherUIOptions
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Point>) InvocationPoint() const;
        WINRT_IMPL_AUTO(void) InvocationPoint(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Point> const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Rect>) SelectionRect() const;
        WINRT_IMPL_AUTO(void) SelectionRect(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Rect> const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::Popups::Placement) PreferredPlacement() const;
        WINRT_IMPL_AUTO(void) PreferredPlacement(winrt::Windows::UI::Popups::Placement const& value) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherUIOptions>
    {
        template <typename D> using type = consume_Windows_System_ILauncherUIOptions<D>;
    };
    template <typename D>
    struct consume_Windows_System_ILauncherViewOptions
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::ViewManagement::ViewSizePreference) DesiredRemainingView() const;
        WINRT_IMPL_AUTO(void) DesiredRemainingView(winrt::Windows::UI::ViewManagement::ViewSizePreference const& value) const;
    };
    template <> struct consume<winrt::Windows::System::ILauncherViewOptions>
    {
        template <typename D> using type = consume_Windows_System_ILauncherViewOptions<D>;
    };
    template <typename D>
    struct consume_Windows_System_IMemoryManagerStatics
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) AppMemoryUsage() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) AppMemoryUsageLimit() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::AppMemoryUsageLevel) AppMemoryUsageLevel() const;
        WINRT_IMPL_AUTO(winrt::event_token) AppMemoryUsageIncreased(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        using AppMemoryUsageIncreased_revoker = impl::event_revoker<winrt::Windows::System::IMemoryManagerStatics, &impl::abi_t<winrt::Windows::System::IMemoryManagerStatics>::remove_AppMemoryUsageIncreased>;
        [[nodiscard]] AppMemoryUsageIncreased_revoker AppMemoryUsageIncreased(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) AppMemoryUsageIncreased(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) AppMemoryUsageDecreased(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        using AppMemoryUsageDecreased_revoker = impl::event_revoker<winrt::Windows::System::IMemoryManagerStatics, &impl::abi_t<winrt::Windows::System::IMemoryManagerStatics>::remove_AppMemoryUsageDecreased>;
        [[nodiscard]] AppMemoryUsageDecreased_revoker AppMemoryUsageDecreased(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) AppMemoryUsageDecreased(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) AppMemoryUsageLimitChanging(winrt::Windows::Foundation::EventHandler<winrt::Windows::System::AppMemoryUsageLimitChangingEventArgs> const& handler) const;
        using AppMemoryUsageLimitChanging_revoker = impl::event_revoker<winrt::Windows::System::IMemoryManagerStatics, &impl::abi_t<winrt::Windows::System::IMemoryManagerStatics>::remove_AppMemoryUsageLimitChanging>;
        [[nodiscard]] AppMemoryUsageLimitChanging_revoker AppMemoryUsageLimitChanging(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::System::AppMemoryUsageLimitChangingEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) AppMemoryUsageLimitChanging(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::System::IMemoryManagerStatics>
    {
        template <typename D> using type = consume_Windows_System_IMemoryManagerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IMemoryManagerStatics2
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::AppMemoryReport) GetAppMemoryReport() const;
        WINRT_IMPL_AUTO(winrt::Windows::System::ProcessMemoryReport) GetProcessMemoryReport() const;
    };
    template <> struct consume<winrt::Windows::System::IMemoryManagerStatics2>
    {
        template <typename D> using type = consume_Windows_System_IMemoryManagerStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IMemoryManagerStatics3
    {
        WINRT_IMPL_AUTO(bool) TrySetAppMemoryUsageLimit(uint64_t value) const;
    };
    template <> struct consume<winrt::Windows::System::IMemoryManagerStatics3>
    {
        template <typename D> using type = consume_Windows_System_IMemoryManagerStatics3<D>;
    };
    template <typename D>
    struct consume_Windows_System_IMemoryManagerStatics4
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) ExpectedAppMemoryUsageLimit() const;
    };
    template <> struct consume<winrt::Windows::System::IMemoryManagerStatics4>
    {
        template <typename D> using type = consume_Windows_System_IMemoryManagerStatics4<D>;
    };
    template <typename D>
    struct consume_Windows_System_IProcessLauncherOptions
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Storage::Streams::IInputStream) StandardInput() const;
        WINRT_IMPL_AUTO(void) StandardInput(winrt::Windows::Storage::Streams::IInputStream const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Storage::Streams::IOutputStream) StandardOutput() const;
        WINRT_IMPL_AUTO(void) StandardOutput(winrt::Windows::Storage::Streams::IOutputStream const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Storage::Streams::IOutputStream) StandardError() const;
        WINRT_IMPL_AUTO(void) StandardError(winrt::Windows::Storage::Streams::IOutputStream const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) WorkingDirectory() const;
        WINRT_IMPL_AUTO(void) WorkingDirectory(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Windows::System::IProcessLauncherOptions>
    {
        template <typename D> using type = consume_Windows_System_IProcessLauncherOptions<D>;
    };
    template <typename D>
    struct consume_Windows_System_IProcessLauncherResult
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint32_t) ExitCode() const;
    };
    template <> struct consume<winrt::Windows::System::IProcessLauncherResult>
    {
        template <typename D> using type = consume_Windows_System_IProcessLauncherResult<D>;
    };
    template <typename D>
    struct consume_Windows_System_IProcessLauncherStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::ProcessLauncherResult>) RunToCompletionAsync(param::hstring const& fileName, param::hstring const& args) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::ProcessLauncherResult>) RunToCompletionAsync(param::hstring const& fileName, param::hstring const& args, winrt::Windows::System::ProcessLauncherOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::System::IProcessLauncherStatics>
    {
        template <typename D> using type = consume_Windows_System_IProcessLauncherStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IProcessMemoryReport
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) PrivateWorkingSetUsage() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint64_t) TotalWorkingSetUsage() const;
    };
    template <> struct consume<winrt::Windows::System::IProcessMemoryReport>
    {
        template <typename D> using type = consume_Windows_System_IProcessMemoryReport<D>;
    };
    template <typename D>
    struct consume_Windows_System_IProtocolForResultsOperation
    {
        WINRT_IMPL_AUTO(void) ReportCompleted(winrt::Windows::Foundation::Collections::ValueSet const& data) const;
    };
    template <> struct consume<winrt::Windows::System::IProtocolForResultsOperation>
    {
        template <typename D> using type = consume_Windows_System_IProtocolForResultsOperation<D>;
    };
    template <typename D>
    struct consume_Windows_System_IRemoteLauncherOptions
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Uri) FallbackUri() const;
        WINRT_IMPL_AUTO(void) FallbackUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVector<hstring>) PreferredAppIds() const;
    };
    template <> struct consume<winrt::Windows::System::IRemoteLauncherOptions>
    {
        template <typename D> using type = consume_Windows_System_IRemoteLauncherOptions<D>;
    };
    template <typename D>
    struct consume_Windows_System_IRemoteLauncherStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::RemoteLaunchUriStatus>) LaunchUriAsync(winrt::Windows::System::RemoteSystems::RemoteSystemConnectionRequest const& remoteSystemConnectionRequest, winrt::Windows::Foundation::Uri const& uri) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::RemoteLaunchUriStatus>) LaunchUriAsync(winrt::Windows::System::RemoteSystems::RemoteSystemConnectionRequest const& remoteSystemConnectionRequest, winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::RemoteLauncherOptions const& options) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::RemoteLaunchUriStatus>) LaunchUriAsync(winrt::Windows::System::RemoteSystems::RemoteSystemConnectionRequest const& remoteSystemConnectionRequest, winrt::Windows::Foundation::Uri const& uri, winrt::Windows::System::RemoteLauncherOptions const& options, winrt::Windows::Foundation::Collections::ValueSet const& inputData) const;
    };
    template <> struct consume<winrt::Windows::System::IRemoteLauncherStatics>
    {
        template <typename D> using type = consume_Windows_System_IRemoteLauncherStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IShutdownManagerStatics
    {
        WINRT_IMPL_AUTO(void) BeginShutdown(winrt::Windows::System::ShutdownKind const& shutdownKind, winrt::Windows::Foundation::TimeSpan const& timeout) const;
        WINRT_IMPL_AUTO(void) CancelShutdown() const;
    };
    template <> struct consume<winrt::Windows::System::IShutdownManagerStatics>
    {
        template <typename D> using type = consume_Windows_System_IShutdownManagerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IShutdownManagerStatics2
    {
        WINRT_IMPL_AUTO(bool) IsPowerStateSupported(winrt::Windows::System::PowerState const& powerState) const;
        WINRT_IMPL_AUTO(void) EnterPowerState(winrt::Windows::System::PowerState const& powerState) const;
        WINRT_IMPL_AUTO(void) EnterPowerState(winrt::Windows::System::PowerState const& powerState, winrt::Windows::Foundation::TimeSpan const& wakeUpAfter) const;
    };
    template <> struct consume<winrt::Windows::System::IShutdownManagerStatics2>
    {
        template <typename D> using type = consume_Windows_System_IShutdownManagerStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_System_ITimeZoneSettingsStatics
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) CurrentTimeZoneDisplayName() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<hstring>) SupportedTimeZoneDisplayNames() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) CanChangeTimeZone() const;
        WINRT_IMPL_AUTO(void) ChangeTimeZoneByDisplayName(param::hstring const& timeZoneDisplayName) const;
    };
    template <> struct consume<winrt::Windows::System::ITimeZoneSettingsStatics>
    {
        template <typename D> using type = consume_Windows_System_ITimeZoneSettingsStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_ITimeZoneSettingsStatics2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::AutoUpdateTimeZoneStatus>) AutoUpdateTimeZoneAsync(winrt::Windows::Foundation::TimeSpan const& timeout) const;
    };
    template <> struct consume<winrt::Windows::System::ITimeZoneSettingsStatics2>
    {
        template <typename D> using type = consume_Windows_System_ITimeZoneSettingsStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUser
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) NonRoamableId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::UserAuthenticationStatus) AuthenticationStatus() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::UserType) Type() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::IInspectable>) GetPropertyAsync(param::hstring const& value) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IPropertySet>) GetPropertiesAsync(param::async_vector_view<hstring> const& values) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Storage::Streams::IRandomAccessStreamReference>) GetPictureAsync(winrt::Windows::System::UserPictureSize const& desiredSize) const;
    };
    template <> struct consume<winrt::Windows::System::IUser>
    {
        template <typename D> using type = consume_Windows_System_IUser<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserAuthenticationStatusChangeDeferral
    {
        WINRT_IMPL_AUTO(void) Complete() const;
    };
    template <> struct consume<winrt::Windows::System::IUserAuthenticationStatusChangeDeferral>
    {
        template <typename D> using type = consume_Windows_System_IUserAuthenticationStatusChangeDeferral<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserAuthenticationStatusChangingEventArgs
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::UserAuthenticationStatusChangeDeferral) GetDeferral() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) User() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::UserAuthenticationStatus) NewStatus() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::UserAuthenticationStatus) CurrentStatus() const;
    };
    template <> struct consume<winrt::Windows::System::IUserAuthenticationStatusChangingEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IUserAuthenticationStatusChangingEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserChangedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) User() const;
    };
    template <> struct consume<winrt::Windows::System::IUserChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IUserChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserChangedEventArgs2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::System::UserWatcherUpdateKind>) ChangedPropertyKinds() const;
    };
    template <> struct consume<winrt::Windows::System::IUserChangedEventArgs2>
    {
        template <typename D> using type = consume_Windows_System_IUserChangedEventArgs2<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserDeviceAssociationChangedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) DeviceId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) NewUser() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) OldUser() const;
    };
    template <> struct consume<winrt::Windows::System::IUserDeviceAssociationChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_System_IUserDeviceAssociationChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserDeviceAssociationStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::User) FindUserFromDeviceId(param::hstring const& deviceId) const;
        WINRT_IMPL_AUTO(winrt::event_token) UserDeviceAssociationChanged(winrt::Windows::Foundation::EventHandler<winrt::Windows::System::UserDeviceAssociationChangedEventArgs> const& handler) const;
        using UserDeviceAssociationChanged_revoker = impl::event_revoker<winrt::Windows::System::IUserDeviceAssociationStatics, &impl::abi_t<winrt::Windows::System::IUserDeviceAssociationStatics>::remove_UserDeviceAssociationChanged>;
        [[nodiscard]] UserDeviceAssociationChanged_revoker UserDeviceAssociationChanged(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::System::UserDeviceAssociationChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) UserDeviceAssociationChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::System::IUserDeviceAssociationStatics>
    {
        template <typename D> using type = consume_Windows_System_IUserDeviceAssociationStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserPicker
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) AllowGuestAccounts() const;
        WINRT_IMPL_AUTO(void) AllowGuestAccounts(bool value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::User) SuggestedSelectedUser() const;
        WINRT_IMPL_AUTO(void) SuggestedSelectedUser(winrt::Windows::System::User const& value) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::System::User>) PickSingleUserAsync() const;
    };
    template <> struct consume<winrt::Windows::System::IUserPicker>
    {
        template <typename D> using type = consume_Windows_System_IUserPicker<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserPickerStatics
    {
        WINRT_IMPL_AUTO(bool) IsSupported() const;
    };
    template <> struct consume<winrt::Windows::System::IUserPickerStatics>
    {
        template <typename D> using type = consume_Windows_System_IUserPickerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::System::UserWatcher) CreateWatcher() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::System::User>>) FindAllAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::System::User>>) FindAllAsync(winrt::Windows::System::UserType const& type) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::System::User>>) FindAllAsync(winrt::Windows::System::UserType const& type, winrt::Windows::System::UserAuthenticationStatus const& status) const;
        WINRT_IMPL_AUTO(winrt::Windows::System::User) GetFromId(param::hstring const& nonRoamableId) const;
    };
    template <> struct consume<winrt::Windows::System::IUserStatics>
    {
        template <typename D> using type = consume_Windows_System_IUserStatics<D>;
    };
    template <typename D>
    struct consume_Windows_System_IUserWatcher
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::System::UserWatcherStatus) Status() const;
        WINRT_IMPL_AUTO(void) Start() const;
        WINRT_IMPL_AUTO(void) Stop() const;
        WINRT_IMPL_AUTO(winrt::event_token) Added(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        using Added_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_Added>;
        [[nodiscard]] Added_revoker Added(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Added(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Removed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        using Removed_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_Removed>;
        [[nodiscard]] Removed_revoker Removed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Removed(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Updated(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        using Updated_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_Updated>;
        [[nodiscard]] Updated_revoker Updated(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Updated(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) AuthenticationStatusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        using AuthenticationStatusChanged_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_AuthenticationStatusChanged>;
        [[nodiscard]] AuthenticationStatusChanged_revoker AuthenticationStatusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) AuthenticationStatusChanged(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) AuthenticationStatusChanging(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserAuthenticationStatusChangingEventArgs> const& handler) const;
        using AuthenticationStatusChanging_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_AuthenticationStatusChanging>;
        [[nodiscard]] AuthenticationStatusChanging_revoker AuthenticationStatusChanging(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::System::UserAuthenticationStatusChangingEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) AuthenticationStatusChanging(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) EnumerationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using EnumerationCompleted_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_EnumerationCompleted>;
        [[nodiscard]] EnumerationCompleted_revoker EnumerationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) EnumerationCompleted(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Stopped(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Stopped_revoker = impl::event_revoker<winrt::Windows::System::IUserWatcher, &impl::abi_t<winrt::Windows::System::IUserWatcher>::remove_Stopped>;
        [[nodiscard]] Stopped_revoker Stopped(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::System::UserWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) Stopped(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::System::IUserWatcher>
    {
        template <typename D> using type = consume_Windows_System_IUserWatcher<D>;
    };
}
#endif
