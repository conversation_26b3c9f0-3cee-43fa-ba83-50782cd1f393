// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_ClosedCaptioning_1_H
#define WINRT_Windows_Media_ClosedCaptioning_1_H
#include "winrt/impl/Windows.Media.ClosedCaptioning.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::ClosedCaptioning
{
    struct __declspec(empty_bases) IClosedCaptionPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IClosedCaptionPropertiesStatics>
    {
        IClosedCaptionPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IClosedCaptionPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
