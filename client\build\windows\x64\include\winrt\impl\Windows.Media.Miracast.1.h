// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_Miracast_1_H
#define WINRT_Windows_Media_Miracast_1_H
#include "winrt/impl/Windows.Media.Miracast.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Miracast
{
    struct __declspec(empty_bases) IMiracastReceiver :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiver>
    {
        IMiracastReceiver(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiver(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverApplySettingsResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverApplySettingsResult>
    {
        IMiracastReceiverApplySettingsResult(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverApplySettingsResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverConnection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverConnection>
    {
        IMiracastReceiverConnection(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverConnectionCreatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverConnectionCreatedEventArgs>
    {
        IMiracastReceiverConnectionCreatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverConnectionCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverCursorImageChannel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverCursorImageChannel>
    {
        IMiracastReceiverCursorImageChannel(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverCursorImageChannel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverCursorImageChannelSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverCursorImageChannelSettings>
    {
        IMiracastReceiverCursorImageChannelSettings(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverCursorImageChannelSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverDisconnectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverDisconnectedEventArgs>
    {
        IMiracastReceiverDisconnectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverDisconnectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverGameControllerDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverGameControllerDevice>
    {
        IMiracastReceiverGameControllerDevice(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverGameControllerDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverInputDevices :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverInputDevices>
    {
        IMiracastReceiverInputDevices(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverInputDevices(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverKeyboardDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverKeyboardDevice>
    {
        IMiracastReceiverKeyboardDevice(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverKeyboardDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverMediaSourceCreatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverMediaSourceCreatedEventArgs>
    {
        IMiracastReceiverMediaSourceCreatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverMediaSourceCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverSession>
    {
        IMiracastReceiverSession(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverSessionStartResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverSessionStartResult>
    {
        IMiracastReceiverSessionStartResult(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverSessionStartResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverSettings>
    {
        IMiracastReceiverSettings(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverStatus :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverStatus>
    {
        IMiracastReceiverStatus(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverStatus(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverStreamControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverStreamControl>
    {
        IMiracastReceiverStreamControl(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverStreamControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastReceiverVideoStreamSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastReceiverVideoStreamSettings>
    {
        IMiracastReceiverVideoStreamSettings(std::nullptr_t = nullptr) noexcept {}
        IMiracastReceiverVideoStreamSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMiracastTransmitter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMiracastTransmitter>
    {
        IMiracastTransmitter(std::nullptr_t = nullptr) noexcept {}
        IMiracastTransmitter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
