// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Management_Deployment_1_H
#define WINRT_Windows_Management_Deployment_1_H
#include "winrt/impl/Windows.Management.Deployment.0.h"
WINRT_EXPORT namespace winrt::Windows::Management::Deployment
{
    struct __declspec(empty_bases) IAddPackageOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAddPackageOptions>
    {
        IAddPackageOptions(std::nullptr_t = nullptr) noexcept {}
        IAddPackageOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentResult>
    {
        IDeploymentResult(std::nullptr_t = nullptr) noexcept {}
        IDeploymentResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentResult2>
    {
        IDeploymentResult2(std::nullptr_t = nullptr) noexcept {}
        IDeploymentResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager>
    {
        IPackageManager(std::nullptr_t = nullptr) noexcept {}
        IPackageManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager2>
    {
        IPackageManager2(std::nullptr_t = nullptr) noexcept {}
        IPackageManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager3>
    {
        IPackageManager3(std::nullptr_t = nullptr) noexcept {}
        IPackageManager3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager4>
    {
        IPackageManager4(std::nullptr_t = nullptr) noexcept {}
        IPackageManager4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager5>
    {
        IPackageManager5(std::nullptr_t = nullptr) noexcept {}
        IPackageManager5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager6>
    {
        IPackageManager6(std::nullptr_t = nullptr) noexcept {}
        IPackageManager6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager7>
    {
        IPackageManager7(std::nullptr_t = nullptr) noexcept {}
        IPackageManager7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager8>
    {
        IPackageManager8(std::nullptr_t = nullptr) noexcept {}
        IPackageManager8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManager9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManager9>
    {
        IPackageManager9(std::nullptr_t = nullptr) noexcept {}
        IPackageManager9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageManagerDebugSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageManagerDebugSettings>
    {
        IPackageManagerDebugSettings(std::nullptr_t = nullptr) noexcept {}
        IPackageManagerDebugSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageUserInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageUserInformation>
    {
        IPackageUserInformation(std::nullptr_t = nullptr) noexcept {}
        IPackageUserInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageVolume :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageVolume>
    {
        IPackageVolume(std::nullptr_t = nullptr) noexcept {}
        IPackageVolume(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPackageVolume2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPackageVolume2>
    {
        IPackageVolume2(std::nullptr_t = nullptr) noexcept {}
        IPackageVolume2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRegisterPackageOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegisterPackageOptions>
    {
        IRegisterPackageOptions(std::nullptr_t = nullptr) noexcept {}
        IRegisterPackageOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStagePackageOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStagePackageOptions>
    {
        IStagePackageOptions(std::nullptr_t = nullptr) noexcept {}
        IStagePackageOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
