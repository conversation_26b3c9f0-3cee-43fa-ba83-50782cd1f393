// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_Editing_1_H
#define WINRT_Windows_Media_Editing_1_H
#include "winrt/impl/Windows.Media.Editing.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Editing
{
    struct __declspec(empty_bases) IBackgroundAudioTrack :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundAudioTrack>
    {
        IBackgroundAudioTrack(std::nullptr_t = nullptr) noexcept {}
        IBackgroundAudioTrack(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBackgroundAudioTrackStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundAudioTrackStatics>
    {
        IBackgroundAudioTrackStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundAudioTrackStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEmbeddedAudioTrack :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEmbeddedAudioTrack>
    {
        IEmbeddedAudioTrack(std::nullptr_t = nullptr) noexcept {}
        IEmbeddedAudioTrack(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaClip>
    {
        IMediaClip(std::nullptr_t = nullptr) noexcept {}
        IMediaClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaClipStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaClipStatics>
    {
        IMediaClipStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaClipStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaClipStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaClipStatics2>
    {
        IMediaClipStatics2(std::nullptr_t = nullptr) noexcept {}
        IMediaClipStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaComposition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaComposition>
    {
        IMediaComposition(std::nullptr_t = nullptr) noexcept {}
        IMediaComposition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaComposition2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaComposition2>
    {
        IMediaComposition2(std::nullptr_t = nullptr) noexcept {}
        IMediaComposition2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaCompositionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaCompositionStatics>
    {
        IMediaCompositionStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaCompositionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaOverlay :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlay>
    {
        IMediaOverlay(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlay(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaOverlayFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlayFactory>
    {
        IMediaOverlayFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlayFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaOverlayLayer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlayLayer>
    {
        IMediaOverlayLayer(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlayLayer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaOverlayLayerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlayLayerFactory>
    {
        IMediaOverlayLayerFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlayLayerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
