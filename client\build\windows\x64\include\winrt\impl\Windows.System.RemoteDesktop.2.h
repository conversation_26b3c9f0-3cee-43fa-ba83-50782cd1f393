// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_System_RemoteDesktop_2_H
#define WINRT_Windows_System_RemoteDesktop_2_H
#include "winrt/impl/Windows.System.RemoteDesktop.1.h"
WINRT_EXPORT namespace winrt::Windows::System::RemoteDesktop
{
    struct InteractiveSession
    {
        InteractiveSession() = delete;
        [[nodiscard]] static auto IsRemote();
    };
}
#endif
